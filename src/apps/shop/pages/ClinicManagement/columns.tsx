import { MouseEvent, startTransition } from 'react';

import { ColumnDef, createColumnHelper } from '@tanstack/react-table';

import i18n from '@/libs/i18n';
import { ColorBadge } from '@/libs/ui/ColorBadge/ColorBadge';
import { getClinicVendorStatusParams } from '@/libs/ui/ColorBadge/utils';
import { ClinicType } from '@/types/common.ts';
import { Text } from '@mantine/core';
import styles from './ClinicManagement.module.css';

const columnHelper = createColumnHelper<ClinicType>();

export const getColumns = (
  getData: (clinic?: ClinicType) => Promise<void>,
): ColumnDef<ClinicType>[] =>
  [
    columnHelper.accessor('name', {
      header: i18n.t('superAdmin.dashboard.columns.name'),
      cell: (info) => {
        const get = async () => {
          await getData(info.row.original);
        };
        // to avoid setting 2 records in history
        const handleClick = async (event: MouseEvent<HTMLSpanElement>) => {
          event.preventDefault();
          event.stopPropagation();

          startTransition(get as () => void);
        };

        const name = info.getValue();

        return (
          <a href="#" onClick={handleClick} className={styles.clinicName}>
            <Text fw="700" size="14px">
              {name}
            </Text>
          </a>
        );
      },
      maxSize: 300,
    }),
    // TODO add value manager
    columnHelper.accessor('shippingAddress', {
      header: i18n.t('client.clinicManagement.columns.manager'),
      cell: () => {
        return '--';
      },
    }),
    // TODO add value email
    columnHelper.accessor('billingAddress', {
      header: i18n.t('client.clinicManagement.columns.email'),
      cell: () => {
        return '--';
      },
    }),
    columnHelper.accessor('hasAnyVendorConnected', {
      header: i18n.t('client.clinicManagement.columns.status'),
      cell: (info) => {
        return (
          // Last place with ColorBadge component
          <ColorBadge
            value={info.getValue() ? 'connected' : 'disconnected'}
            paramsMapper={getClinicVendorStatusParams}
          />
        );
      },
    }),
  ] as unknown as ColumnDef<ClinicType>[];
