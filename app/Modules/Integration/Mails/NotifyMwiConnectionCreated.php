<?php

declare(strict_types=1);

namespace App\Modules\Integration\Mails;

use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

final class NotifyMwiConnectionCreated extends Mailable
{
    use Queueable, SerializesModels;

    public function __construct(public readonly IntegrationConnection $integrationConnection) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'New API request',
        );
    }

    public function content(): Content
    {
        $mwiUsername = $this->integrationConnection->credentials['username'] ?? 'N/A';
        $clinicName = $this->integrationConnection->clinic->name ?? 'Unknown Clinic';
        $clinicPhoneNumber = $this->integrationConnection->clinic->phone_number ?? 'N/A';
        $clinicAccountName = $this->integrationConnection->clinic->account->name ?? 'Unknown Account';

        return new Content(
            markdown: 'mail.mwi-connection-created',
            with: [
                'mwiUsername' => $mwiUsername,
                'clinicName' => $clinicName,
                'clinicPhoneNumber' => $clinicPhoneNumber,
                'clinicAccountName' => $clinicAccountName,
            ]
        );
    }
}
