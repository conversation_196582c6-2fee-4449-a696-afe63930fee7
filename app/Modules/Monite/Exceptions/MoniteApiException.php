<?php

declare(strict_types=1);

namespace App\Modules\Monite\Exceptions;

use Exception;
use Illuminate\Http\Client\Response;

final class MoniteApiException extends Exception
{
    public function __construct(
        string $message = '',
        int $code = 0,
        ?Exception $previous = null,
        public readonly ?Response $response = null,
        public readonly ?array $errorDetails = null
    ) {
        parent::__construct($message, $code, $previous);
    }

    public static function fromResponse(Response $response): self
    {
        $errorDetails = $response->json();

        $message = $errorDetails['message'] ?? $errorDetails['error'] ?? 'Monite API request failed';

        if (is_array($message)) {
            $message = json_encode($message);
        }

        return new self(
            message: $message,
            code: $response->status(),
            response: $response,
            errorDetails: $errorDetails
        );
    }

    public static function authenticationFailed(string $message = 'Authentication failed'): self
    {
        return new self($message, 401);
    }

    public static function rateLimitExceeded(string $message = 'Rate limit exceeded'): self
    {
        return new self($message, 429);
    }

    public function getErrorCode(): ?string
    {
        return $this->errorDetails['error_code'] ?? null;
    }

    public function getErrorType(): ?string
    {
        return $this->errorDetails['error_type'] ?? null;
    }
}
