<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Models;

use App\Modules\CatalogSync\Enums\CatalogSyncBatchStatus;
use Database\Factories\CatalogSyncBatchFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Prunable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

final class CatalogSyncBatch extends Model
{
    use HasFactory, HasUuids, Prunable, SoftDeletes;

    protected $guarded = [];

    protected $attributes = [
        'status' => CatalogSyncBatchStatus::Pending,
    ];

    protected $casts = [
        'status' => CatalogSyncBatchStatus::class,
        'message' => 'array',
        'started_at' => 'datetime',
        'finished_at' => 'datetime',
    ];

    public static function newFactory(): CatalogSyncBatchFactory
    {
        return CatalogSyncBatchFactory::new();
    }

    public function prunable(): Builder
    {
        return self::where('created_at', '<=', now()->subDays(7));
    }

    public function task(): BelongsTo
    {
        return $this->belongsTo(CatalogSyncTask::class, 'catalog_sync_task_id');
    }

    public function pending(): bool
    {
        return $this->status === CatalogSyncBatchStatus::Pending;
    }

    public function success(): bool
    {
        return $this->status === CatalogSyncBatchStatus::Completed;
    }
}
