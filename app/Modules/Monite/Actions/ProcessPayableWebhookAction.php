<?php

declare(strict_types=1);

namespace App\Modules\Monite\Actions;

use App\Models\Clinic;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Models\MoniteWebhookEvent;
use App\Modules\Monite\Services\MonitePayableProcessor;
use App\Modules\Monite\Traits\ChecksMoniteFeatureFlag;
use Illuminate\Support\Facades\Log;
use Throwable;

final class ProcessPayableWebhookAction
{
    use ChecksMoniteFeatureFlag;

    public function __construct(
        private readonly MoniteApiClientInterface $apiClient,
        private readonly MonitePayableProcessor $payableProcessor
    ) {}

    /**
     * Process a payable webhook event
     */
    public function execute(MoniteWebhookEvent $webhookEvent, Clinic $clinic): void
    {
        // Check if Monite is enabled for this clinic
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'processPayableWebhook')) {
            $webhookEvent->markAsIgnored('Monite feature disabled for this clinic');

            return;
        }

        try {
            $webhookEvent->markAsProcessing();

            $payableId = $webhookEvent->payload['object']['id'];
            $payableDetails = $this->fetchPayableDetails($payableId, $clinic->monite_entity_id);
            $result = $this->payableProcessor->process($payableDetails, $clinic);

            Log::info('Payable webhook processed', [
                'event_id' => $webhookEvent->event_id,
                'payable_id' => $payableId,
                'clinic_id' => $clinic->id,
                'matched' => $result['matched'],
                'invoice_id' => $result['invoice_id'] ?? null,
            ]);

            $webhookEvent->markAsProcessed();
        } catch (Throwable $e) {
            Log::error('Error processing payable webhook', [
                'event_id' => $webhookEvent->event_id,
                'error' => $e->getMessage(),
            ]);

            $webhookEvent->markAsFailed("Error: {$e->getMessage()}");
            throw $e;
        }
    }

    /**
     * Fetch complete payable details from Monite API
     */
    private function fetchPayableDetails(string $payableId, string $entityId): array
    {
        $response = $this->apiClient
            ->withEntityId($entityId)
            ->get("/payables/{$payableId}");

        return $response->json();
    }
}
