<?php

declare(strict_types=1);

namespace App\Modules\Monite\Models;

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\Order\Models\ExternalOrder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MoniteInvoice extends Model
{
    use HasFactory;
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'clinic_id',
        'vendor_id',
        'external_order_id',
        'invoice_number',
        'status',
        'amount',
        'currency',
        'due_date',
        'vendor_name',
        'monite_payable_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'due_date' => 'date',
        'amount' => 'decimal:2',
    ];

    /**
     * Get the clinic that owns the invoice.
     */
    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    /**
     * Get the vendor that owns the invoice.
     */
    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    /**
     * Get the external order associated with this invoice, if any.
     */
    public function externalOrder(): BelongsTo
    {
        return $this->belongsTo(ExternalOrder::class);
    }
}
