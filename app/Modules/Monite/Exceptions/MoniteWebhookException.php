<?php

declare(strict_types=1);

namespace App\Modules\Monite\Exceptions;

use Exception;

class MoniteWebhookException extends Exception
{
    public function __construct(
        string $message = 'Monite webhook error',
        int $code = 0,
        ?Exception $previous = null,
        public readonly ?array $context = null
    ) {
        parent::__construct($message, $code, $previous);
    }

    public static function invalidSignature(): self
    {
        return new self('Invalid webhook signature', 401);
    }

    public static function invalidPayload(array $context = []): self
    {
        return new self('Invalid webhook payload', 400, null, $context);
    }

    public static function entityNotFound(string $entityId): self
    {
        return new self(
            "No clinic found for Monite entity ID: {$entityId}",
            404,
            null,
            ['entity_id' => $entityId]
        );
    }

    public static function payableNotFound(string $payableId): self
    {
        return new self(
            "Payable not found: {$payableId}",
            404,
            null,
            ['payable_id' => $payableId]
        );
    }

    public static function webhookProcessingFailed(string $message, array $context = []): self
    {
        return new self(
            "Webhook processing failed: {$message}",
            500,
            null,
            $context
        );
    }

    public function getContext(): ?array
    {
        return $this->context;
    }
}
