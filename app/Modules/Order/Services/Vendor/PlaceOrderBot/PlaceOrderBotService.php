<?php

declare(strict_types=1);

namespace App\Modules\Order\Services\Vendor\PlaceOrderBot;

use App\Enums\OrderItemStatus;
use App\Exceptions\VendorServiceError;
use App\Models\SubOrder;
use App\Modules\Integration\Enums\IntegrationPoint;
use App\Modules\Integration\Enums\IntegrationSessionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Order\Services\Vendor\Contracts\OrderProcessor;
use App\Modules\Order\Services\Vendor\Exceptions\OrderValidationException;
use Throwable;

final class PlaceOrderBotService implements OrderProcessor
{
    private PlaceOrderBotHttpClient $client;

    /**
     * Create a new PlaceOrderBot instance.
     */
    public function __construct(

    ) {
        $this->client = new PlaceOrderBotHttpClient();
    }

    public function validateOrder(SubOrder $suborder, IntegrationConnection $integrationConnection): void
    {
        foreach ($suborder->items as $item) {
            if (empty($item->productOffer?->vendor_sku)) {
                throw new OrderValidationException(
                    'Order item missing vendor SKU',
                    [
                        'item_id' => $item->id,
                        'product_offer_id' => $item->product_offer_id ?? null,
                    ]
                );
            }
        }
    }

    /**
     * Place an order with the vendor.
     *
     * @throws VendorServiceError
     */
    public function processOrder(SubOrder $subOrder, IntegrationConnection $integrationConnection): void
    {
        $session = $integrationConnection->sessions()->create([
            'integration_point' => IntegrationPoint::PlaceOrders,
            'status' => IntegrationSessionStatus::Started,
            'started_at' => now(),
        ]);
        try {
            $orderNumber = $subOrder->order->order_number;

            $payload = [
                'subOrderId' => $subOrder->id,
                'vendorId' => $subOrder->vendor->slug,
                'email' => $integrationConnection->credentials['email'] ?? $integrationConnection->credentials['username'] ?? null,
                'password' => $integrationConnection->credentials['password'] ?? null,
                'reference' => $orderNumber,
                'notification' => config('highfive.orders.bcc_email', '<EMAIL>'),
                'items' => $subOrder->items->filter(function ($item) {
                    return $item->status !== OrderItemStatus::NonTrackable;
                })->map(function ($item) {
                    return [
                        'sku' => $item->productOffer->vendor_sku,
                        'quantity' => $item->quantity,
                    ];
                })->toArray(),
            ];
            $this->client->post(
                '/api/placeOrder',
                $payload,
                $session,
                'PlaceOrderBotService::processOrder',
                $subOrder
            );

            $subOrder->update([
                'error_code' => null,
                'error_message' => null,
            ]);

            $subOrder->items()->ignoreTrackable()->update([
                'status' => OrderItemStatus::Accepted,
                'error_message' => null,
            ]);
            $session->success();
        } catch (Throwable $e) {
            $session->failed();
            throw $e;
        }
    }
}
