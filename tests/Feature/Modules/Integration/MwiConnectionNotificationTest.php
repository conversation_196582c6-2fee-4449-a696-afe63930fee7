<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Enums\ClinicAccountRole;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Integration\Mails\NotifyMwiConnectionCreated;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;

beforeEach(function () {
    Config::set('highfive.mwi.api_access_request_email', '<EMAIL>');

    $this->account = ClinicAccount::factory()->create();

    /** @var User $this->user */
    $this->user = User::factory()->for($this->account, 'account')->create();
    $this->user->assignRole(ClinicAccountRole::Owner);

    $this->clinic = Clinic::factory()->for($this->account, 'account')->create(['name' => 'Test Clinic']);

    $this->mwiVendor = Vendor::factory()->create(['key' => 'MWIX', 'name' => 'MWI']);
    $this->otherVendor = Vendor::factory()->create(['key' => 'OTHER', 'name' => 'Other Vendor']);
});

it('sends notification email when MWI connection is created', function () {
    Mail::fake();

    $connection = IntegrationConnection::factory()->create([
        'vendor_id' => $this->mwiVendor->id,
        'clinic_id' => $this->clinic->id,
        'credentials' => ['username' => 'test_mwi_user', 'password' => 'secret'],
    ]);

    Mail::assertQueued(NotifyMwiConnectionCreated::class, function ($mail) use ($connection) {
        return $mail->integrationConnection->credentials['username'] === $connection->credentials['username'];
    });

    Mail::assertQueuedCount(1);
});

it('does not send notification email when non-MWI connection is created', function () {
    Mail::fake();

    IntegrationConnection::factory()->create([
        'vendor_id' => $this->otherVendor->id,
        'clinic_id' => $this->clinic->id,
        'credentials' => ['username' => 'test_user', 'password' => 'secret'],
    ]);

    Mail::assertNotQueued(NotifyMwiConnectionCreated::class);
    Mail::assertQueuedCount(0);
});

it('does not send notification email when notification email is not configured', function () {
    Mail::fake();
    Config::set('highfive.mwi.api_access_request_email', null);

    IntegrationConnection::factory()->create([
        'vendor_id' => $this->mwiVendor->id,
        'clinic_id' => $this->clinic->id,
        'credentials' => ['username' => 'test_mwi_user', 'password' => 'secret'],
    ]);

    Mail::assertNotQueued(NotifyMwiConnectionCreated::class);
    Mail::assertQueuedCount(0);
});

it('includes MWI username and clinic details in email content', function () {
    Mail::fake();

    $connection = IntegrationConnection::factory()->create([
        'vendor_id' => $this->mwiVendor->id,
        'clinic_id' => $this->clinic->id,
        'credentials' => ['username' => 'test_mwi_user', 'password' => 'secret'],
    ]);

    Mail::assertQueued(NotifyMwiConnectionCreated::class, function ($mail) {
        $content = $mail->content();

        return $content->with['mwiUsername'] === 'test_mwi_user'
            && $content->with['clinicName'] === 'Test Clinic'
            && isset($content->with['clinicPhoneNumber'])
            && isset($content->with['clinicAccountName']);
    });
});
