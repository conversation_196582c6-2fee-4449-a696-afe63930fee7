<?php

declare(strict_types=1);

use App\Models\Product;
use App\Models\ProductAlternative;

test('product can have alternatives', function () {
    $productA = Product::factory()->create(['name' => 'Product A']);
    $productB = Product::factory()->create(['name' => 'Product B']);

    $productA->alternatives()->create([
        'alternative_id' => $productB->id,
    ]);

    expect($productA->alternatives)->toHaveCount(1);
    expect($productA->alternatives->first()->alternative->name)->toBe('Product B');
});

test('asymmetric relationship creates reverse automatically', function () {
    $productA = Product::factory()->create(['name' => 'Product A']);
    $productB = Product::factory()->create(['name' => 'Product B']);

    // Create A → B
    ProductAlternative::create([
        'product_id' => $productA->id,
        'alternative_id' => $productB->id,
    ]);

    // Check that A → B exists
    expect(ProductAlternative::where('product_id', $productA->id)
        ->where('alternative_id', $productB->id)
        ->exists())->toBeTrue();

    // Check that B → A was automatically created
    expect(ProductAlternative::where('product_id', $productB->id)
        ->where('alternative_id', $productA->id)
        ->exists())->toBeTrue();
});

test('deleting one relationship does not delete reverse', function () {
    $productA = Product::factory()->create(['name' => 'Product A']);
    $productB = Product::factory()->create(['name' => 'Product B']);

    // Create A → B (which automatically creates B → A)
    $alternativeAB = ProductAlternative::create([
        'product_id' => $productA->id,
        'alternative_id' => $productB->id,
    ]);

    // Verify both relationships exist
    expect(ProductAlternative::where('product_id', $productA->id)
        ->where('alternative_id', $productB->id)
        ->exists())->toBeTrue();
    expect(ProductAlternative::where('product_id', $productB->id)
        ->where('alternative_id', $productA->id)
        ->exists())->toBeTrue();

    // Delete A → B using query builder to avoid composite key issues
    ProductAlternative::where('product_id', $productA->id)
        ->where('alternative_id', $productB->id)
        ->delete();

    // Verify A → B is deleted but B → A still exists
    expect(ProductAlternative::where('product_id', $productA->id)
        ->where('alternative_id', $productB->id)
        ->exists())->toBeFalse();
    expect(ProductAlternative::where('product_id', $productB->id)
        ->where('alternative_id', $productA->id)
        ->exists())->toBeTrue();
});

test('cannot create duplicate relationships', function () {
    $productA = Product::factory()->create(['name' => 'Product A']);
    $productB = Product::factory()->create(['name' => 'Product B']);

    // Create first relationship
    ProductAlternative::create([
        'product_id' => $productA->id,
        'alternative_id' => $productB->id,
    ]);

    // Attempting to create the same relationship should fail due to unique constraint
    expect(function () use ($productA, $productB) {
        ProductAlternative::create([
            'product_id' => $productA->id,
            'alternative_id' => $productB->id,
        ]);
    })->toThrow(Illuminate\Database\QueryException::class);
});
