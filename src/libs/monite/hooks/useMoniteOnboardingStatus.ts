import { useQuery } from '@tanstack/react-query';
import { fetchApi } from '@/libs/utils/api';

export interface MoniteOnboardingStatusResponse {
  entityId: string;
  entityStatus: string;
  verificationStatus: string | null;
  onboardingRequirements: {
    paymentMethod?: string;
    requirements?: {
      currentlyDue?: string[];
      eventuallyDue?: string[];
      pastDue?: string[];
      pendingVerification?: string[];
      currentDeadline?: string | null;
    };
    requirementsErrors?: Array<{
      code: string;
      reason: string;
      requirement: string;
    }>;
    verificationErrors?: Array<{
      code: string;
      details: string;
    }>;
    verificationStatus?: string;
    disabledReason?: string | null;
  } | null;
  lastSyncedAt: string | null;
  isOnboardingComplete: boolean;
}

interface UseMoniteOnboardingStatusOptions {
  clinicId?: string;
  enabled?: boolean;
}

const useMoniteOnboardingStatus = ({
  clinicId,
  enabled = true,
}: UseMoniteOnboardingStatusOptions = {}) => {
  return useQuery({
    queryKey: ['monite-onboarding-status', clinicId],
    queryFn: async (): Promise<MoniteOnboardingStatusResponse> => {
      if (!clinicId) {
        throw new Error(
          'Clinic ID is required to check Monite onboarding status',
        );
      }

      const response = await fetchApi<MoniteOnboardingStatusResponse>(
        '/monite/onboarding-status',
        {
          method: 'GET',
          withApi: true,
          authStrategy: 'cookie',
        },
      );

      return response;
    },
    enabled: enabled && !!clinicId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
    refetchOnWindowFocus: true, // Refetch when window regains focus to check updated status
  });
};

// Utility hook that combines onboarding status check with easy access
export const useMoniteOnboardingData = (clinicId?: string) => {
  const { data, isLoading, error, refetch } = useMoniteOnboardingStatus({
    clinicId,
  });

  return {
    isOnboardingComplete: data?.isOnboardingComplete ?? false,
    entityStatus: data?.entityStatus,
    verificationStatus: data?.verificationStatus,
    onboardingRequirements: data?.onboardingRequirements,
    entityId: data?.entityId,
    lastSyncedAt: data?.lastSyncedAt,
    isLoading,
    error,
    refetch,
  };
};
