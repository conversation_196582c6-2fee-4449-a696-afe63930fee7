<?php

declare(strict_types=1);

namespace App\Models\QueryBuilders;

use App\Enums\OrderItemStatus;
use Carbon\Carbon;
use DB;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

final class OrderItemQuery extends Builder
{
    public function ignoreTrackable(): self
    {
        return $this->where('status', '!=', OrderItemStatus::NonTrackable);
    }

    /**
     * Eager load the product history for the given clinic based on date.
     */
    public function getHistory(string $clinicId, string $productId, Carbon $startDate, Carbon $endDate): Collection
    {
        return $this->join('orders', 'orders.id', '=', 'order_items.order_id')
            ->where('order_items.product_offer_id', $productId)
            ->where('orders.clinic_id', $clinicId)
            ->where('orders.created_at', '>=', $startDate->startOfDay())
            ->where('orders.created_at', '<=', $endDate->endOfDay())
            ->select([
                'orders.created_at',
                DB::raw('SUM(order_items.quantity) as quantity'),
                DB::raw('SUM(order_items.quantity * order_items.price) as total_spent'),
                DB::raw('COUNT(DISTINCT orders.id) as order_count'),
            ])
            ->groupBy('orders.created_at')
            ->get()->groupBy(function ($row) {
                return Carbon::parse($row->created_at)->format('Y-m');
            })
            ->map(function ($group) {
                return (object) [
                    'month' => $group->first()->created_at->format('Y-m'),
                    'quantity' => (int) $group->sum('quantity'),
                    'total_spent' => (float) $group->sum('total_spent'),
                    'order_count' => (int) $group->sum('order_count'),
                ];
            })
            ->values();
    }
}
