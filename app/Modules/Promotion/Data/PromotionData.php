<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Data;

use App\Http\Resources\Cart\ProductOffer;
use App\Models\ProductOffer as ProductOfferModel;
use App\Modules\Order\Data\VendorData;
use App\Modules\Promotion\Enums\ActionType;
use App\Modules\Promotion\Enums\ConditionType;
use App\Modules\Promotion\Enums\PromotionType;
use DateTimeImmutable;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Log;
use Spatie\LaravelData\Attributes\Hidden;
use Spatie\LaravelData\Attributes\WithTransformer;
use Spatie\LaravelData\Data;
use Spatie\LaravelData\Transformers\DateTimeInterfaceTransformer;

final class PromotionData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly PromotionType $type,
        public readonly ?string $description,
        #[WithTransformer(DateTimeInterfaceTransformer::class, format: 'Y-m-d')]
        public readonly DateTimeImmutable $startedAt,
        #[WithTransformer(DateTimeInterfaceTransformer::class, format: 'Y-m-d')]
        public readonly ?DateTimeImmutable $endedAt,
        public readonly VendorData $vendor,
        #[Hidden]
        public readonly ?Collection $productOffers = null,
        #[Hidden]
        public readonly ?Collection $rules = null,
    ) {}

    public function with(): array
    {
        return [
            'offers' => $this->productOffers ? ProductOffer::collection($this->productOffers) : [],
            'requirements' => $this->getRequirements(),
            'benefits' => $this->getBenefits(),
            'keyword' => $this->getPromotionKeyword(),
        ];
    }

    private function getRequirements(): array
    {
        if (! $this->rules) {
            return [];
        }

        $requirements = [];

        foreach ($this->rules as $rule) {
            foreach ($rule->conditions as $condition) {
                switch ($condition->type) {
                    case ConditionType::MinimumSpendAmount:
                        $requirements[] = [
                            'type' => ConditionType::MinimumSpendAmount,
                            'description' => 'Minimum spend required',
                            'amount' => $condition->config['minimum_spend_amount'] ?? 0,
                        ];
                        break;
                    case ConditionType::MinimumYearOverYearSpendGrowthPercent:
                        $requirements[] = [
                            'type' => ConditionType::MinimumYearOverYearSpendGrowthPercent,
                            'description' => 'Year-over-year spend growth required',
                            'percentage' => $condition->config['minimum_year_over_year_spend_growth_percent'] ?? 0,
                        ];
                        break;
                    case ConditionType::MinimumQuantity:
                        $requirements[] = [
                            'type' => ConditionType::MinimumQuantity,
                            'description' => 'Minimum quantity required',
                            'minimum_quantity' => $condition->config['quantity'] ?? 0,
                        ];
                        break;
                    default:
                        Log::warning('Unknown promotion condition type encountered', [
                            'condition_type' => $condition->type->value ?? 'unknown',
                            'promotion_id' => $this->id,
                        ]);
                        break;
                }
            }
        }

        return $requirements;
    }

    private function getBenefits(): array
    {
        if (! $this->rules) {
            return [];
        }

        $benefits = [];

        foreach ($this->rules as $rule) {
            foreach ($rule->actions as $action) {
                switch ($action->type) {
                    case ActionType::UpdateRebateEstimate:
                        $benefits[] = [
                            'type' => ActionType::UpdateRebateEstimate,
                            'description' => 'Rebate on qualifying purchases',
                            'percentage' => $action->config['rebate_percent'] ?? 0,
                        ];
                        break;
                    case ActionType::GiveFreeProduct:
                        $freeProductOfferId = $action->config['free_product_offer_id'] ?? null;
                        $freeProductOffer = null;

                        if ($freeProductOfferId) {
                            $productOfferModel = ProductOfferModel::with(['vendor', 'clinics', 'product'])->find($freeProductOfferId);
                            if ($productOfferModel) {
                                $freeProductOffer = new ProductOffer($productOfferModel);
                            }
                        }

                        $benefits[] = [
                            'type' => ActionType::GiveFreeProduct,
                            'description' => 'Buy X Get Y promotion',
                            'quantity' => $action->config['quantity'] ?? 0,
                            'message' => $action->config['message'] ?? null,
                            'free_product_offer' => $freeProductOffer,
                        ];
                        break;
                    default:
                        Log::warning('Unknown promotion action type encountered', [
                            'action_type' => $action->type->value ?? 'unknown',
                            'promotion_id' => $this->id,
                        ]);
                        break;
                }
            }
        }

        return $benefits;
    }

    private function getPromotionKeyword(): string
    {
        $vendorName = $this->vendor->name ?? null;

        return $this->buildPromotionKeyword(
            $vendorName,
            $this->type->value,
            $this->name
        );
    }

    /**
     * Build a promotion keyword for search indexing.
     * Same logic as Product::buildPromotionKeyword()
     */
    private function buildPromotionKeyword($vendor, $type, $name): string
    {
        $parts = array_filter([$vendor, $type, $name]);
        $keyword = implode('-', $parts);
        $keyword = mb_strtolower(str_replace(' ', '-', $keyword));

        return 'promo:'.$keyword;
    }
}
