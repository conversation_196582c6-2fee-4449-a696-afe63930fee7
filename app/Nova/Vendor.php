<?php

declare(strict_types=1);

namespace App\Nova;

use App\Enums\ExpenseCategory;
use App\Enums\VendorAuthenticationKind;
use App\Enums\VendorType;
use App\Modules\Integration\Enums\IntegrationPoint;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use Laravel\Nova\Fields\Code;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\HasOne;
use Laravel\Nova\Fields\Image;
use Laravel\Nova\Fields\MultiSelect;
use <PERSON>vel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\NovaRequest;

final class Vendor extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Vendor>
     */
    public static $model = \App\Models\Vendor::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'key',
        'name',
        'slug',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Text::make('Key', 'key')
                ->help('This value must be unique and consist of uppercase letters only. Example: ABCD, WXYZ')
                ->readonly(fn (NovaRequest $request) => $request->isUpdateOrUpdateAttachedRequest())
                ->sortable()
                ->rules('required', 'uppercase', 'alpha', 'max:4', 'unique:vendors,key')
                ->showOnPreview(),

            Text::make('Name', 'name')
                ->sortable()
                ->rules('required', 'max:255')
                ->showOnPreview(),

            Text::make('Slug', 'slug')
                ->required()
                ->sortable()
                ->maxlength(255)
                ->creationRules('alpha_dash:ascii', 'unique:vendors,slug')
                ->updateRules('alpha_dash:ascii', 'unique:vendors,slug,{{resourceId}}')
                ->showOnPreview(),

            Image::make('Image', 'image_path')
                ->acceptedTypes('image/*')
                ->path('vendor-images')
                ->disableDownload()
                ->prunable()
                ->deletable()
                ->indexWidth(64)
                ->showOnPreview(),

            Select::make('Type', 'type')
                ->options(VendorType::options())
                ->displayUsingLabels()
                ->sortable()
                ->rules('required')
                ->showOnPreview()
                ->hideFromIndex(fn (NovaRequest $request) => $request->viaRelationship()),

            Select::make('Expense Category', 'expense_category')
                ->help('This category will be used to calculate the clinic\'s cart budget summary.')
                ->options(ExpenseCategory::options())
                ->displayUsingLabels()
                ->sortable()
                ->rules('required')
                ->hideFromIndex(),

            Boolean::make('Enabled', 'is_enabled')
                ->sortable()
                ->rules('required'),

            Text::make('Purchase Order Email', 'purchase_order_email')
                ->help('This email will receive the purchase order emails when a clinic orders a product from this vendor. The email is not sent if there\'s an automated integration for the vendor.')
                ->required()
                ->rules('required', 'email'),

            Text::make('New Account Email', 'new_account_email')
                ->sortable()
                ->rules('nullable', 'email')
                ->hideFromIndex(),

            Text::make('Phone Number', 'phone_number')
                ->sortable()
                ->rules('nullable', 'max:255')
                ->hideFromIndex(),

            Select::make('Authentication Kind', 'authentication_kind')
                ->options(VendorAuthenticationKind::options())
                ->displayUsingLabels()
                ->sortable()
                ->rules('required')
                ->hideFromIndex(),

            Code::make('Authentication Configuration', 'authentication_configuration')
                ->json()
                ->hideFromIndex(),

            MultiSelect::make('Integration Points', 'integration_points')
                ->options(IntegrationPoint::toOptionsWithLabels())
                ->displayUsingLabels()
                ->hideFromIndex(),

            HasMany::make('Account Receivable Contacts', 'accountReceivableContacts', VendorAccountReceivableContact::class),

            HasOne::make('Shipping Terms', 'shippingTerms', VendorShippingTerms::class),

            HasMany::make('Products', 'productOffers', ProductOffer::class),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [];
    }
}
