<?php

declare(strict_types=1);

namespace App\Modules\Monite\Console\Commands;

use App\Models\Clinic;
use App\Modules\Monite\Exceptions\MoniteApiException;
use App\Modules\Monite\Jobs\SyncMoniteUsersJob;
use App\Modules\Monite\Services\MoniteFeatureFlagService;
use Illuminate\Support\Facades\Log;
use Throwable;

final class SyncMoniteUsers extends BaseMoniteCommand
{
    protected $signature = 'monite:sync-users {--clinic-id= : Sync users for a specific clinic} {--queue : Use queue for job processing}';

    protected $description = 'Sync Monite users for all clinics or a specific clinic';

    public function __construct(
        MoniteFeatureFlagService $featureFlagService
    ) {
        parent::__construct($featureFlagService);
    }

    protected function processClinic(Clinic $clinic): int
    {
        $useQueue = $this->option('queue');

        try {
            if ($useQueue) {
                SyncMoniteUsersJob::dispatch($clinic);
                $this->line("User sync queued for: {$clinic->name}");

                return 1;
            }
            $job = new SyncMoniteUsersJob($clinic);
            $action = app(\App\Modules\Monite\Actions\SyncUsersAction::class);
            $job->handle($action);
            $this->line("Users synced for: {$clinic->name}");

            return 1;

        } catch (MoniteApiException $e) {
            if (str_contains($e->getMessage(), 'has no roles assigned')) {
                $this->warn("Skipping user sync for {$clinic->name}: {$e->getMessage()}");

                return 0;
            }

            $this->error("Error syncing users for {$clinic->name}: {$e->getMessage()}");
            Log::error('User sync error', [
                'clinic_id' => $clinic->id,
                'error' => $e->getMessage(),
            ]);

            return 0;
        } catch (Throwable $e) {
            $this->error("Unexpected error syncing users for {$clinic->name}: {$e->getMessage()}");
            Log::error('Unexpected user sync error', [
                'clinic_id' => $clinic->id,
                'error' => $e->getMessage(),
            ]);

            return 0;
        }
    }

    protected function getActionName(): string
    {
        return 'User sync';
    }
}
