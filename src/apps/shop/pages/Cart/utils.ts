import i18n from '@/libs/i18n';
import { CartVendorType } from '@/libs/cart/types';
import { SavedItemType } from '@/types';

export const getItemsText = (count: number = 0) =>
  count === 1
    ? i18n.t('client.cart.singleItem')
    : i18n.t('client.cart.pluralItems', { count });

export const getSubtotalItemsText = (count: number = 0) =>
  count === 1
    ? i18n.t('client.cart.subtotalSingleItem')
    : i18n.t('client.cart.subtotalPluralItems', { count });

export const buildVendorCartItemsData = (
  vendors: CartVendorType[],
  savedItems: SavedItemType[],
): (CartVendorType & { savedItems: SavedItemType[] })[] => {
  const allItems = [...vendors, ...savedItems];

  const groupedVendors = allItems.reduce(
    (vendorsMap, currentItem) => {
      const isSavedItem = 'productOffer' in currentItem;

      const vendorId = isSavedItem
        ? currentItem.productOffer.vendor.id
        : currentItem.id;

      const existingVendorData = vendorsMap[vendorId] ?? {
        savedItems: [],
        items: [],
        id: vendorId,
      };

      const updatedVendorData = isSavedItem
        ? {
            ...existingVendorData,
            items: existingVendorData.items ?? [],
            savedItems: [...(existingVendorData.savedItems ?? []), currentItem],
            imageUrl:
              existingVendorData.imageUrl ??
              currentItem.productOffer.vendor.imageUrl,
          }
        : {
            ...existingVendorData,
            ...currentItem,
          };

      vendorsMap[vendorId] = updatedVendorData;
      return vendorsMap;
    },
    {} as Record<string, CartVendorType & { savedItems: SavedItemType[] }>,
  );

  return Object.values(groupedVendors);
};
