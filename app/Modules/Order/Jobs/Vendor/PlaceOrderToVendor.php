<?php

declare(strict_types=1);

namespace App\Modules\Order\Jobs\Vendor;

use App\Enums\OrderItemStatus;
use App\Exceptions\VendorServiceError;
use App\Jobs\BaseVendorJob;
use App\Models\SubOrder;
use App\Modules\Order\Enums\VendorError;
use App\Modules\Order\Events\OrderPlacementFailed;
use App\Modules\Order\Events\Vendor\OrderPlaced;
use App\Modules\Order\Services\Vendor\Contracts\OrderProcessor;
use App\Modules\Order\Services\Vendor\Exceptions\ApiAuthenticationException;
use App\Modules\Order\Services\Vendor\Exceptions\ApiException;
use App\Modules\Order\Services\Vendor\Exceptions\ApiValidationException;
use App\Modules\Order\Services\Vendor\Exceptions\OrderValidationException;
use App\Modules\Order\Services\Vendor\Exceptions\VendorException;
use App\Modules\Order\Services\Vendor\Factory;
use Illuminate\Support\Facades\Log;
use Throwable;

final class PlaceOrderToVendor extends BaseVendorJob
{
    public function __construct(private SubOrder $suborder)
    {
        parent::__construct($suborder->order->clinic_id, $suborder->vendor_id);
    }

    public function handle(): void
    {

        try {
            $vendorConnection = $this->getVendorConnection();
            $service = Factory::make($vendorConnection);

            if (! $service instanceof OrderProcessor) {
                Log::warning('Vendor service does not support order processing');

                return;
            }

            $service->validateOrder($this->suborder, $vendorConnection);
            $service->processOrder($this->suborder, $vendorConnection);

            OrderPlaced::dispatch($this->suborder);

        } catch (ApiAuthenticationException|ApiException  $e) {
            throw $e;
        } catch (VendorException|ApiValidationException|OrderValidationException $e) {
            $this->suborder->update([
                'error_code' => VendorError::safeValue($e->errorCode),
                'error_message' => $e->getMessage().' '.json_encode($e->context, JSON_PRETTY_PRINT),
            ]);
            $this->suborder->items()->ignoreTrackable()->update([
                'status' => OrderItemStatus::PlacementFailed,
            ]);
        } catch (VendorServiceError $e) {
            $this->suborder->update([
                'error_code' => VendorError::VENDOR_ERROR,
                'error_message' => $e->getMessage().' '.json_encode($e->context, JSON_PRETTY_PRINT),
            ]);
            $this->suborder->items()->ignoreTrackable()->update([
                'status' => OrderItemStatus::PlacementFailed,
            ]);
        } catch (Throwable $th) {
            Log::error('An unexpected error occurred while placing order to vendor', [
                'clinic_id' => $this->suborder->order->clinic_id,
                'vendor_id' => $this->suborder->vendor_id,
                'suborder_id' => $this->suborder->id,
                'error' => $th->getMessage(),
                'trace' => $th->getTraceAsString(),
            ]);
            $this->suborder->update([
                'error_code' => VendorError::UNKNOWN_ERROR,
                'error_message' => $th->getMessage(),
            ]);
            $this->suborder->items()->ignoreTrackable()->update([
                'status' => OrderItemStatus::PlacementFailed,
            ]);

            throw new VendorServiceError(
                'An unexpected error occurred while placing order to vendor',
                context: [
                    'clinic_id' => $this->suborder->order->clinic_id,
                    'vendor_id' => $this->suborder->vendor_id,
                    'suborder_id' => $this->suborder->id,
                    'trace' => $th->getTraceAsString(),
                ],
                previous: $th,
            );
        }
    }

    public function failed(?Throwable $th): void
    {
        parent::failed($th);

        // Update error state only when all retries have failed
        if ($th instanceof ApiAuthenticationException || $th instanceof ApiException) {
            $this->suborder->update([
                'error_code' => VendorError::safeValue($th->errorCode),
                'error_message' => $th->getMessage().' '.json_encode($th->context, JSON_PRETTY_PRINT),
            ]);
            $this->suborder->items()->ignoreTrackable()->update([
                'status' => OrderItemStatus::PlacementFailed,
            ]);
        }

        event(new OrderPlacementFailed($this->suborder, $th));
    }
}
