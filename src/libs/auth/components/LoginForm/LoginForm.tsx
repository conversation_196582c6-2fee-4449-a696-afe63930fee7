import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import * as Yup from 'yup';

import { Input } from '@/libs/form/Input';
import { Checkbox } from '@/libs/form/Checkbox';
import { Button } from '@/libs/ui/Button/Button';
import { AuthFormWrapper } from '../AuthFormWrapper/AuthFormWrapper';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { defaultFormErrorHandler } from '@/utils';
import { ApiErrorProps } from '@/types/utility';
import styles from './LoginForm.module.css';
import { useRememberMe } from '@/libs/auth/hooks/useRememberMe';

interface LoginFormProps {
  onSuccess: (userData: unknown) => void;
  onError?: (error: ApiErrorProps) => void;
  apiFunc: (values: FormValues) => Promise<unknown>;
  isLoading?: boolean;
  forgotPasswordPath: string;
  schema: Yup.ObjectSchema<FormValues>;
  namespace?: string;
}

export interface FormValues {
  email: string;
  password: string;
  rememberMe: boolean;
}

export const LoginForm = ({
  onSuccess,
  onError,
  apiFunc,
  isLoading: externalLoading = false,
  forgotPasswordPath,
  schema,
  namespace = 'login',
}: LoginFormProps) => {
  const { t } = useTranslation();
  const {
    register,
    handleSubmit,
    formState: { errors },
    setError,
    reset,
  } = useForm<FormValues>({
    resolver: yupResolver(schema),
  });

  const { setRememberMeValues } = useRememberMe({
    onLoadValues: reset,
  });

  const { apiRequest: handleLogin, isLoading: internalLoading } =
    useAsyncRequest({
      apiFunc: async (values?: FormValues) => {
        if (!values) return;
        const result = await apiFunc(values);
        onSuccess(result);
      },
      errorFunc: (error) => {
        defaultFormErrorHandler(error, setError);
        onError?.(error);
      },
    });

  const isLoading = externalLoading || internalLoading;

  const handleLoginSubmit = handleSubmit((values) => {
    if (values.rememberMe) {
      setRememberMeValues(values);
    }
    handleLogin(values);
  });

  return (
    <AuthFormWrapper
      title={t(`${namespace}.title`)}
      subtitle={t(`${namespace}.subtitle`)}
    >
      <form onSubmit={handleLoginSubmit}>
        <Input
          label={t('form.field.email')}
          placeholder={t('form.field.email')}
          {...register('email')}
          disabled={isLoading}
          size="md"
          error={errors.email?.message}
        />

        <Input
          type="password"
          label={t('form.field.password')}
          placeholder={t('form.field.password')}
          {...register('password')}
          disabled={isLoading}
          size="md"
          error={errors.password?.message}
          autoComplete="off"
        />

        <div className={styles.info}>
          <Checkbox
            label={t(`${namespace}.rememberMe`)}
            {...register('rememberMe')}
          />

          <Link to={forgotPasswordPath}>
            {t(`${namespace}.forgotPassword`)}
          </Link>
        </div>
        <Button
          type="submit"
          loading={isLoading}
          className={styles.submitButton}
          data-testid="loginButton"
        >
          {t(`${namespace}.signIn`)}
        </Button>
      </form>
    </AuthFormWrapper>
  );
};
