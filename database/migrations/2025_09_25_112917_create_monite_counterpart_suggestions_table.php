<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Monite\Enums\MoniteCounterpartSuggestionStatus;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('monite_counterpart_suggestions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignIdFor(Clinic::class)->constrained()->cascadeOnDelete();
            $table->uuid('monite_counterpart_id');
            $table->string('monite_counterpart_name');
            $table->foreignIdFor(Vendor::class, 'suggested_vendor_id')->nullable()->constrained()->nullOnDelete();
            $table->string('suggested_vendor_name')->nullable();
            $table->string('status')->default(MoniteCounterpartSuggestionStatus::PENDING->value);
            $table->foreignIdFor(User::class, 'approved_by')->nullable()->constrained()->nullOnDelete();
            $table->timestamp('approved_at')->nullable();
            $table->text('rejected_reason')->nullable();
            $table->foreignUuid('webhook_event_id')->nullable()->constrained('monite_webhook_events')->nullOnDelete();
            $table->timestamps();

            // Indexes for performance
            $table->index(['clinic_id', 'status']);
            $table->index(['monite_counterpart_id', 'clinic_id']);
            $table->index('status');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('monite_counterpart_suggestions');
    }
};
