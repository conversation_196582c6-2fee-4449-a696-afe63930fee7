import { Flex, Text } from '@mantine/core';
import { Menu, MenuItem } from '@/libs/ui/Menu/Menu';
import { Button } from '@/libs/ui/Button/Button';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import type { ClinicType } from '@/types/common';
import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import SwapArrowIcon from './assets/swap-arrow.svg?react';
import BackIcon from './assets/back.svg?react';

interface SwapClinicProps {
  list: ClinicType[];
  current: ClinicType;
}
export const SwapClinic = ({ current, list }: SwapClinicProps) => {
  const { setActiveClinic } = useAccountStore();
  const navigate = useNavigate();
  const { t } = useTranslation();

  const handleClinicSelect = (clinic: ClinicType) => {
    if (current.id !== clinic.id) {
      setActiveClinic({
        id: clinic.id,
        name: clinic.name,
        hasAnyVendorConnected: clinic.hasAnyVendorConnected,
        // TODO: Status hardcoded
        status: 'DONE',
      });
      window.location.reload();
    }
  };

  return (
    <Menu
      trigger={
        <Flex>
          <Text size="1.25rem" fw="700" mr="0.75rem">
            {current.name}
          </Text>
          <Button variant="unstyled">
            <SwapArrowIcon />
          </Button>
        </Flex>
      }
      side="bottom"
      align="start"
    >
      {list
        .filter(({ id }) => id !== current.id)
        .map((clinic) => (
          <MenuItem key={clinic.id} onClick={() => handleClinicSelect(clinic)}>
            {clinic.name}
          </MenuItem>
        ))}
      <MenuItem onClick={() => navigate(SHOP_ROUTES_PATH.clinicManagement)}>
        <div className="flex items-center">
          <BackIcon className="mr-2" />
          {t('sidebar.clinicManagement')}
        </div>
      </MenuItem>
    </Menu>
  );
};
