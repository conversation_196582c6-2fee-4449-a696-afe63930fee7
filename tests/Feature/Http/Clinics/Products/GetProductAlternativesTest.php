<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Product;
use App\Models\ProductAlternative;
use App\Models\ProductOffer;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Account\Models\ClinicAccount;
use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Models\IntegrationConnection;
use App\Modules\Product\Models\ProductManufacturer;

beforeEach(function () {
    $this->account = ClinicAccount::factory()->create();
    $this->user = User::factory()->create([
        'account_id' => $this->account->id,
    ]);
    $this->clinic = Clinic::factory()->create([
        'clinic_account_id' => $this->account->id,
    ]);

    $this->manufacturer = ProductManufacturer::factory()->create(['name' => 'Test Manufacturer']);

    // Create main product
    $this->product = Product::factory()->create([
        'name' => 'Main Product',
        'product_manufacturer_id' => $this->manufacturer->id,
    ]);

    // Create alternative products
    $this->alternative1 = Product::factory()->create([
        'name' => 'Alternative Product 1',
        'product_manufacturer_id' => $this->manufacturer->id,
    ]);

    $this->alternative2 = Product::factory()->create([
        'name' => 'Alternative Product 2',
        'product_manufacturer_id' => $this->manufacturer->id,
    ]);

    // Create product alternatives relationships
    ProductAlternative::create([
        'product_id' => $this->product->id,
        'alternative_id' => $this->alternative1->id,
    ]);

    ProductAlternative::create([
        'product_id' => $this->product->id,
        'alternative_id' => $this->alternative2->id,
    ]);
});

it('returns product alternatives with correct structure', function () {
    $response = $this->actingAs($this->user)
        ->getJson("/api/clinics/{$this->clinic->id}/products/{$this->product->id}/alternatives");

    $response->assertSuccessful()
        ->assertJsonStructure([
            '*' => [
                'id',
                'name',
                'imageUrl',
                'isFavorite',
                'manufacturer',
                'manufacturerSku',
                'description',
                'offers' => [
                    '*' => [
                        'id',
                        'vendor' => [
                            'id',
                            'name',
                            'imageUrl',
                            'type',
                        ],
                        'vendorSku',
                        'price',
                        'clinicPrice',
                        'stockStatus',
                        'lastOrderedAt',
                        'lastOrderedQuantity',
                        'increments',
                        'isRecommended',
                        'unitOfMeasure',
                        'size',
                        'raw_category_1',
                        'raw_category_2',
                        'raw_category_3',
                        'raw_category_4',
                    ],
                ],
                'attributes',
                'isHazardous',
                'requiresPrescription',
                'requiresColdShipping',
                'isControlledSubstance',
                'isControlled222Form',
                'requiresPedigree',
                'nationalDrugCode',
            ],
        ]);

    $data = $response->json();
    expect($data)->toHaveCount(2);

    // Check first alternative
    expect($data[0]['name'])->toBe('Alternative Product 1');
    expect($data[0]['manufacturer'])->toBe('Test Manufacturer');
    expect($data[0]['isFavorite'])->toBeFalse();
    expect($data[0])->toHaveKey('offers');
    expect($data[0])->toHaveKey('attributes');
    expect($data[0])->toHaveKey('isHazardous');

    // Check second alternative
    expect($data[1]['name'])->toBe('Alternative Product 2');
    expect($data[1]['manufacturer'])->toBe('Test Manufacturer');
    expect($data[1]['isFavorite'])->toBeFalse();
    expect($data[1])->toHaveKey('offers');
    expect($data[1])->toHaveKey('attributes');
    expect($data[1])->toHaveKey('isHazardous');
});

it('returns empty array when product has no alternatives', function () {
    $productWithoutAlternatives = Product::factory()->create(['name' => 'Product Without Alternatives']);

    $response = $this->actingAs($this->user)
        ->getJson("/api/clinics/{$this->clinic->id}/products/{$productWithoutAlternatives->id}/alternatives");

    $response->assertSuccessful()
        ->assertJson([]);
});

it('requires authentication', function () {
    $response = $this->getJson("/api/clinics/{$this->clinic->id}/products/{$this->product->id}/alternatives");

    $response->assertUnauthorized();
});

it('requires clinic authorization', function () {
    // Create a different clinic account
    $otherAccount = ClinicAccount::factory()->create();
    $otherClinic = Clinic::factory()->create([
        'clinic_account_id' => $otherAccount->id,
    ]);

    $response = $this->actingAs($this->user)
        ->getJson("/api/clinics/{$otherClinic->id}/products/{$this->product->id}/alternatives");

    $response->assertForbidden();
});

it('returns product with offers only for connected vendors', function () {
    // Create vendors and product offers for the alternative products
    $connectedVendor = Vendor::factory()->enabled()->create();
    $unconnectedVendor = Vendor::factory()->enabled()->create();

    // Create product offers for alternative1
    $connectedOffer = ProductOffer::factory()->create([
        'product_id' => $this->alternative1->id,
        'vendor_id' => $connectedVendor->id,
        'price' => 5000,
    ]);

    $unconnectedOffer = ProductOffer::factory()->create([
        'product_id' => $this->alternative1->id,
        'vendor_id' => $unconnectedVendor->id,
        'price' => 4500,
    ]);

    // Attach both offers to clinic
    $this->clinic->productOffers()->attach($connectedOffer);
    $this->clinic->productOffers()->attach($unconnectedOffer);

    // Create integration connection only for the first vendor
    IntegrationConnection::create([
        'vendor_id' => $connectedVendor->id,
        'clinic_id' => $this->clinic->id,
        'status' => IntegrationConnectionStatus::Connected,
        'credentials' => ['username' => 'test', 'password' => 'test'],
    ]);

    $response = $this->actingAs($this->user)
        ->getJson("/api/clinics/{$this->clinic->id}/products/{$this->product->id}/alternatives");

    $response->assertSuccessful();

    $data = $response->json();
    $alternative1Data = collect($data)->firstWhere('name', 'Alternative Product 1');

    expect($alternative1Data)->not->toBeNull();
    expect($alternative1Data['offers'])->not->toBeEmpty();

    // Assert that only the connected vendor's offer is returned
    $offerIds = collect($alternative1Data['offers'])->pluck('id')->toArray();
    expect($offerIds)->toContain($connectedOffer->id);
    expect($offerIds)->not->toContain($unconnectedOffer->id);
});
