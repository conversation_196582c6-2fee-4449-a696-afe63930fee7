import * as Yup from 'yup';
import i18n from '@/libs/i18n';

// Helper function to create number validation with transformation
const createNumberValidation = (
  fieldKey: string,
  options: { required?: boolean } = {},
) => {
  const fieldPath = i18n.t(`client.settings.budget.${fieldKey}`);
  const { required = true } = options;

  let validation = Yup.number()
    .transform((value, originalValue) => {
      if (typeof originalValue === 'string') {
        const cleaned = originalValue.replace(/[^0-9.-]+/g, '');
        return Number(cleaned);
      }
      return value;
    })
    .min(0, i18n.t('form.errorMessage.positive', { path: fieldPath }));

  if (required) {
    validation = validation.required(
      i18n.t('form.errorMessage.required', { path: fieldPath }),
    );
  } else {
    validation = validation.optional();
  }

  return validation;
};

export const STATIC_SCHEMA = Yup.object().shape({
  weeklyCogs: createNumberValidation('weeklyCOGS'),
  monthlyCogs: createNumberValidation('monthlyCOGS'),
  weeklyGa: createNumberValidation('weeklyGA'),
  monthlyGa: createNumberValidation('monthlyGA'),
  externalWeeklyCogs: createNumberValidation('externalWeeklyCogs', {
    required: false,
  }),
  externalMonthlyCogs: createNumberValidation('externalMonthlyCogs', {
    required: false,
  }),
});

export const DYNAMIC_SCHEMA = Yup.object().shape({
  targetCogsPercent: createNumberValidation('COGSTarget'),
  targetGaPercent: createNumberValidation('GATarget'),
  avgTwoWeeksSales: createNumberValidation('avgTwoWeeksSales'),
  monthToDateSales: createNumberValidation('monthToDateSales'),
  externalWeeklyCogs: createNumberValidation('externalWeeklyCogs', {
    required: false,
  }),
  externalMonthlyCogs: createNumberValidation('externalMonthlyCogs', {
    required: false,
  }),
});
