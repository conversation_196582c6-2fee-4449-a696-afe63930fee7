<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\Monite\Models\MoniteVendorMapping;
use App\Modules\Monite\Services\VendorFuzzyMatcher;

beforeEach(function () {
    $this->fuzzyMatcher = app(VendorFuzzyMatcher::class);
});

it('finds exact matches with perfect similarity', function () {
    $vendor = Vendor::factory()->create(['name' => 'Acme Corporation']);

    $match = $this->fuzzyMatcher->findBestMatch('Acme Corporation');

    expect($match)->not->toBeNull();
    expect($match->id)->toBe($vendor->id);
});

it('finds similar matches with high similarity', function () {
    $vendor = Vendor::factory()->create(['name' => 'Acme Corp']);

    $match = $this->fuzzyMatcher->findBestMatch('Acme Corporation');

    expect($match)->not->toBeNull();
    expect($match->id)->toBe($vendor->id);
});

it('finds vendors regardless of existing mappings', function () {
    $vendor = Vendor::factory()->create(['name' => 'Acme Corporation']);
    MoniteVendorMapping::factory()->create([
        'vendor_id' => $vendor->id,
        'clinic_id' => Clinic::factory()->create()->id,
    ]);

    $match = $this->fuzzyMatcher->findBestMatch('Acme Corporation');

    expect($match)->not->toBeNull();
    expect($match->id)->toBe($vendor->id);
});

it('finds disabled vendors', function () {
    $vendor = Vendor::factory()->create(['name' => 'Acme Corporation', 'is_enabled' => false]);

    $match = $this->fuzzyMatcher->findBestMatch('Acme Corporation');

    expect($match)->not->toBeNull();
    expect($match->id)->toBe($vendor->id);
});

it('returns the best match when multiple vendors exist', function () {
    $vendor1 = Vendor::factory()->create(['name' => 'Acme Corp']);
    $vendor2 = Vendor::factory()->create(['name' => 'Acme Corporation']);
    $vendor3 = Vendor::factory()->create(['name' => 'Acme Corp Ltd']);

    $match = $this->fuzzyMatcher->findBestMatch('Acme Corporation');

    expect($match)->not->toBeNull();
    // Should return one of the matching vendors
    expect($match->name)->toContain('Acme');
});

it('returns one match even with many similar vendors', function () {
    // Create many similar vendors
    for ($i = 0; $i < 10; $i++) {
        Vendor::factory()->create(['name' => "Acme Corp {$i}"]);
    }

    $match = $this->fuzzyMatcher->findBestMatch('Acme Corporation');

    expect($match)->not->toBeNull();
    expect($match->name)->toContain('Acme');
});

it('handles case insensitive matching', function () {
    $vendor = Vendor::factory()->create(['name' => 'ACME CORPORATION']);

    $match = $this->fuzzyMatcher->findBestMatch('acme corporation');

    expect($match)->not->toBeNull();
    expect($match->id)->toBe($vendor->id);
});

it('handles special characters in names', function () {
    $vendor = Vendor::factory()->create(['name' => 'Acme Corp. Inc.']);

    $match = $this->fuzzyMatcher->findBestMatch('Acme Corp Inc');

    expect($match)->not->toBeNull();
    expect($match->id)->toBe($vendor->id);
});
