<?php

declare(strict_types=1);

namespace App\Actions\Products;

use App\Models\Clinic;
use App\Models\Product;

final class AutoComplete
{
    public function __construct(
        private readonly SearchEngine $action
    ) {}

    public function handle(
        Clinic $clinic,
        string $searchTerm,
    ): array {

        [$products] = $this->action->handle(
            $clinic,
            $searchTerm,
            []
        );

        if (! $products?->exists()) {
            return [];
        }

        $productNames = $products->get()->filter(function (Product $product) use ($searchTerm) {
            return str_starts_with(mb_strtolower($product->name), mb_strtolower($searchTerm));
        })->map(function (Product $product) {
            return $product->name;
        })->values()->all();

        return $this->normalizeProductNames($productNames, $searchTerm);
    }

    private function normalizeProductNames(array $productNames, string $searchTerm): array
    {
        $prefixes = [];

        foreach ($productNames as $name) {
            $normalized = $this->extractNormalizedPrefix($name);
            if ($normalized && str_starts_with(mb_strtolower($normalized), mb_strtolower($searchTerm))) {
                $prefixes[] = $normalized;
            }
        }

        $uniquePrefixes = array_unique($prefixes);
        usort($uniquePrefixes, function ($a, $b) {
            return mb_strlen($a) - mb_strlen($b);
        });

        return array_values($uniquePrefixes);
    }

    private function extractNormalizedPrefix(string $name): string
    {
        $words = explode(' ', mb_trim($name));
        $filteredWords = [];

        foreach ($words as $index => $word) {
            $cleanWord = mb_trim($word, '.,;:-');

            if ($this->isProductSpecification($cleanWord, $index)) {
                break;
            }

            $filteredWords[] = mb_strtolower($cleanWord);
        }

        return mb_trim(implode(' ', $filteredWords));
    }

    private function isProductSpecification(string $word, int $position): bool
    {
        $lowerWord = mb_strtolower($word);

        if (preg_match('/^\d+/', $word)) {
            return true;
        }

        if (str_starts_with($word, '(')) {
            return true;
        }

        if (str_contains($word, ',')) {
            return true;
        }

        $specificationKeywords = ['for', 'mg', 'lb', 'dose', 'tablet', 'label'];
        if (in_array($lowerWord, $specificationKeywords)) {
            return true;
        }

        if (preg_match('/\d+[a-z]+$/i', $word)) {
            return true;
        }

        if ($position >= 3) {
            return true;
        }

        return false;
    }
}
