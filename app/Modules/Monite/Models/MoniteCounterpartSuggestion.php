<?php

declare(strict_types=1);

namespace App\Modules\Monite\Models;

use App\Models\Clinic;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Monite\Enums\MoniteCounterpartSuggestionStatus;
use Illuminate\Database\Eloquent\Concerns\HasVersion4Uuids as HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

final class MoniteCounterpartSuggestion extends Model
{
    use HasFactory, HasUuids;

    protected $guarded = [];

    protected $casts = [
        'status' => MoniteCounterpartSuggestionStatus::class,
        'approved_at' => 'datetime',
    ];

    public function clinic(): BelongsTo
    {
        return $this->belongsTo(Clinic::class);
    }

    public function suggestedVendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class, 'suggested_vendor_id');
    }

    public function webhookEvent(): BelongsTo
    {
        return $this->belongsTo(MoniteWebhookEvent::class);
    }

    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    public function markAsApproved(string $approvedBy): void
    {
        $this->update([
            'status' => MoniteCounterpartSuggestionStatus::APPROVED,
            'approved_by' => $approvedBy,
            'approved_at' => now(),
        ]);
    }

    public function markAsRejected(string $reason): void
    {
        $this->update([
            'status' => MoniteCounterpartSuggestionStatus::REJECTED,
            'rejected_reason' => $reason,
        ]);
    }
}
