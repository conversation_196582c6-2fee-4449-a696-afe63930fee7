<?php

declare(strict_types=1);

namespace App\Http\Resources\Cart;

use App\Http\Resources\Products\Vendor;
use App\Models\Cart;
use App\Models\Clinic;
use App\Models\ClinicProductFavorite;
use Brick\Money\Money;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class ProductOffer extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $clinicIds = $this->clinics()->pluck('id')->toArray();
        $clinicPrice = $this->whenLoaded('clinics', function () {
            $clinic = $this?->clinics->first();
            if (is_null($clinic)) {
                return null;
            }

            return $clinic?->pivot?->price ?? null;
        }, null);

        $savings = $this->price && $clinicPrice ? Money::ofMinor($this->price - $clinicPrice, 'USD')->getAmount() : Money::ofMinor(0, 'USD')->getAmount();

        return [
            /**
             * The product's unique identifier.
             */
            'id' => $this->id,

            /**
             * The product's display name.
             */
            'name' => $this->name,

            /**
             * The price is determined by either the clinic's custom price or falls back to the product's default price.
             */
            'price' => $this->price ? Money::ofMinor($this->price, 'USD')->getAmount() : null,

            /**
             * The product's clinic price in the cart.
             */
            'clinicPrice' => $clinicPrice ? Money::ofMinor($clinicPrice, 'USD')->getAmount() : null,

            /**
             * product gpo saving
             */
            'gpoSavings' => $this->is_recommended ? $savings : 0,

            /**
             * product vendor saving
             */
            'vendorSavings' => ! $this->is_recommended ? $savings : 0,

            /**
             * Whether the product is purchasable.
             */
            'isPurchasable' => $this->is_purchasable,

            /**
             * Whether the product is marked as recommended by the GPO.
             */
            'isRecommended' => $this->when(isset($this->is_recommended),
                fn () => (bool) $this->is_recommended,
                false
            ),

            /**
             * Whether the product is marked as a favorite by the clinic.
             */
            'isFavorite' => ClinicProductFavorite::when(! empty($clinicIds), function ($query) use ($clinicIds) {
                return $query->whereIn('clinic_id', $clinicIds);
            })
                ->where('product_id', $this->product?->id)
                ->exists() ||
            // Check if this product has a direct favorite relationship
            ($this->clinics?->isNotEmpty() && ClinicProductFavorite::where([
                'clinic_id' => $this->clinics->first()->id,
                'product_id' => $this->product?->id,
            ])->exists()),

            /**
             * The last time the product was ordered by the current clinic.
             */
            'lastOrderedAt' => $this->orderItems->first()?->created_at,

            /**
             * The total quantity of the product that has been ordered by the current clinic.
             */
            'lastOrderedQuantity' => $this->orderItems->first()?->quantity,

            /**
             * The current inventory status of the product.
             *
             * @var \App\Enums\ProductStockStatus
             */
            'stockStatus' => $this->stock_status,

            /**
             * The unique identifier assigned by the vendor for this product.
             */
            'vendorSku' => $this->vendor_sku,

            /**
             * The minimum quantity increments in which this product can be ordered.
             */
            'increments' => $this->increments,

            /**
             * The size of the product.
             */
            'size' => $this->size,

            /**
             * The unit of measure for the product.
             */
            'unit_of_measure' => $this->unit_of_measure,

            /**
             * The vendor/supplier information associated with this product.
             */
            'vendor' => new Vendor($this->vendor),

            /**
             * The quantity of the product in the cart.
             */
            'quantityInCart' => $this->quantity_in_cart ?? 0,
        ];
    }
}
