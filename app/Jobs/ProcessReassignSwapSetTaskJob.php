<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Models\Product;
use App\Models\ProductOffer;
use App\Models\ReassignSwapSetTask;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use RuntimeException;
use Throwable;

final class ProcessReassignSwapSetTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300;

    public function __construct(
        private readonly ReassignSwapSetTask $task
    ) {}

    public function handle(): void
    {
        $this->task->update(['started_at' => now()]);

        if (! Storage::disk('local')->exists($this->task->file_path)) {
            throw new RuntimeException("CSV file not found: {$this->task->file_path}");
        }

        $path = Storage::disk('local')->path($this->task->file_path);
        $delimiter = ',';    // change if needed
        $enclosure = '"';
        $escape = '\\';

        if (! is_readable($path)) {
            throw new RuntimeException("CSV not readable at: {$path}");
        }

        $fh = fopen($path, 'r');
        if ($fh === false) {
            throw new RuntimeException("Failed to open CSV: {$path}");
        }

        $header = fgetcsv($fh, 0, $delimiter, $enclosure, $escape);
        if ($header === false) {
            fclose($fh);
            throw new RuntimeException('CSV appears empty or unreadable header.');
        }

        /**
         * Map header → index, tolerating case and extra spaces.
         */
        $normalized = array_map(fn ($h) => Str::of($h)->trim()->lower()->toString(), $header);
        $colMap = array_flip($normalized);

        $required = ['child_product_id', 'new_parent_product_id'];
        foreach ($required as $col) {
            if (! array_key_exists($col, $colMap)) {
                fclose($fh);
                throw new RuntimeException("Missing required CSV column: {$col}");
            }
        }

        $rowsRead = 0;
        $rowsUpdated = 0;
        $rowsNoop = 0;
        $rowsSkipped = 0;

        while (($row = fgetcsv($fh, 0, $delimiter, $enclosure, $escape)) !== false) {
            $rowsRead++;

            $offerId = mb_trim((string) ($row[$colMap['child_product_id']] ?? ''));
            $newProdId = mb_trim((string) ($row[$colMap['new_parent_product_id']] ?? ''));

            // Ignore if no new parent is provided (explicit requirement)
            if ($newProdId === '') {
                $this->task->events()->create([
                    'type' => 'no_new_parent_product_id',
                    'meta' => $row ?? [],
                ]);
                $rowsSkipped++;

                continue;
            }

            // Basic UUID sanity checks (don’t die on bad data)
            $isUuid = fn ($v) => Str::isUuid($v);
            if (! $isUuid($offerId)) {
                $this->task->events()->create([
                    'type' => 'invalid_child_product_id',
                    'meta' => $row ?? [],
                ]);
                Log::warning('Skip row: child_product_id is not a valid UUID', ['child_product_id' => $offerId]);
                $rowsSkipped++;

                continue;
            }
            if (! $isUuid($newProdId)) {
                $this->task->events()->create([
                    'type' => 'invalid_new_parent_product_id',
                    'meta' => $row ?? [],
                ]);
                Log::warning('Skip row: new_parent_product_id is not a valid UUID', ['new_parent_product_id' => $newProdId, 'child_product_id' => $offerId]);
                $rowsSkipped++;

                continue;
            }

            /** @var ProductOffer|null $offer */
            $offer = ProductOffer::withTrashed()->find($offerId);
            if (! $offer) {
                $this->task->events()->create([
                    'type' => 'product_offer_not_found',
                    'meta' => $row ?? [],
                ]);
                Log::warning('Skip row: product offer not found', ['child_product_id' => $offerId]);
                $rowsSkipped++;

                continue;
            }

            // If it’s already on that product, treat as no-op
            if ($offer->product_id === $newProdId) {
                $this->task->events()->create([
                    'type' => 'noop',
                    'meta' => $row ?? [],
                ]);
                $rowsNoop++;

                continue;
            }

            /** @var Product|null $target */
            $target = Product::withTrashed()->find($newProdId);
            if (! $target) {
                $this->task->events()->create([
                    'type' => 'target_product_not_found',
                    'meta' => $row ?? [],
                ]);
                Log::warning('Skip row: target product not found', ['new_parent_product_id' => $newProdId, 'child_product_id' => $offerId]);
                $rowsSkipped++;

                continue;
            }

            // Conflict guard: avoid violating (vendor_id, product_id) unique constraint
            $vendorId = $offer->vendor_id;
            $conflict = ProductOffer::query()
                ->where('vendor_id', $vendorId)
                ->where('product_id', $newProdId)
                ->whereNull('deleted_at')
                ->where('id', '!=', $offer->id)
                ->exists();

            if ($conflict) {
                $this->task->events()->create([
                    'type' => 'conflict',
                    'meta' => $row ?? [],
                ]);
                Log::warning('Skip row: conflict (same vendor already has an offer under target product)', [
                    'child_product_id' => $offerId,
                    'vendor_id' => $vendorId,
                    'new_parent_product_id' => $newProdId,
                ]);
                $rowsSkipped++;

                continue;
            }

            try {
                DB::transaction(function () use ($offer, $newProdId) {
                    // Reassign
                    $offer->product_id = $newProdId;
                    // touch updated_at via save()
                    $offer->save();
                });

                $rowsUpdated++;
                $this->task->events()->create([
                    'type' => 'offer_reassigned',
                    'meta' => $row ?? [],
                ]);
                Log::info('Offer reassigned', [
                    'product_offer_id' => $offer->id,
                    'old_product_id' => $offer->getOriginal('product_id'),
                    'new_parent_product_id' => $newProdId,
                ]);
            } catch (Throwable $e) {
                // Don’t abort the run; log and continue
                $this->task->events()->create([
                    'type' => 'failed_to_reassign_offer',
                    'meta' => $row ?? [],
                ]);
                Log::error('Failed to reassign offer', [
                    'product_offer_id' => $offerId,
                    'new_parent_product_id' => $newProdId,
                    'error' => $e->getMessage(),
                ]);
                $rowsSkipped++;

                continue;
            }
        }

        fclose($fh);

        $this->task->update([
            'total_rows_count' => $rowsRead,
            'processed_rows_count' => $rowsUpdated + $rowsNoop,
            'skipped_or_failed_rows_count' => $rowsSkipped,
            'finished_at' => now(),
        ]);
    }
}
