<?php

declare(strict_types=1);

namespace Database\Factories\Modules\Monite\Models;

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\Monite\Models\MoniteVendorMapping;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Modules\Monite\Models\MoniteVendorMapping>
 */
final class MoniteVendorMappingFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var class-string<MoniteVendorMapping>
     */
    protected $model = MoniteVendorMapping::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'vendor_id' => Vendor::factory(),
            'clinic_id' => Clinic::factory(),
            'monite_counterpart_id' => $this->faker->uuid(),
        ];
    }
}
