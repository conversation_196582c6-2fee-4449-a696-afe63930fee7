import { Button } from '@/libs/ui/Button/Button';

export const EmptyState = ({
  setActiveTab,
}: {
  setActiveTab: (tab: number) => void;
}) => {
  return (
    <div className="grid place-items-center gap-4 rounded bg-white p-8">
      <span className="font-semibold">You list is empty.</span>
      <span className="text-black/[0.6]">
        You created this list without any products. Please visit the{' '}
        <Button
          variant="unstyled"
          className="underline"
          onClick={() => setActiveTab(1)}
        >
          Previously Purchased
        </Button>{' '}
        tab to add products to your list.
      </span>
    </div>
  );
};
