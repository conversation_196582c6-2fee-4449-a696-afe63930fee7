import { useState } from 'react';
import { Input } from '@/libs/form/Input';
import { ColorPicker } from '@/libs/ui/ColorPicker/ColorPicker';
import { ProductDisplayCard } from '../../../ProductDisplayCard/ProductDisplayCard';

type Props = {
  
};

export const NewList = ({ product, selectedOfferId }) => {
  const [listName, setListName] = useState('My Shopping List 4');
  const [selectedColor, setSelectedColor] = useState('#EF4444');
  const [label, setLabel] = useState('Shop Urgent!!');

  return (
    <div className="space-y-4">
      <div>
        <Input
          label="List name"
          value={listName}
          onChange={(e) => setListName(e.target.value)}
          placeholder="Enter list name"
        />
      </div>

      <ProductDisplayCard product={product} selectedOfferId={selectedOfferId} />

      <div className="grid grid-cols-2 gap-4">
        <ColorPicker
          label="Add color"
          value={selectedColor}
          onChange={setSelectedColor}
        />

        <div>
          <Input
            label="Add label"
            value={label}
            onChange={(e) => setLabel(e.target.value)}
            placeholder="Enter label"
          />
        </div>
      </div>
    </div>
  );
};
