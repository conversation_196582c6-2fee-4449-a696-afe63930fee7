<?php

declare(strict_types=1);

namespace App\Http\Resources\Cart;

use App\Http\Resources\Promotion;
use Brick\Money\Money;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class CartItem extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'quantity' => $this->quantity,
            'price' => Money::ofMinor($this->price, 'USD')->getAmount(),
            'subtotal' => Money::ofMinor($this->total_price, 'USD')->getAmount(),
            'productOfferId' => $this->productOffer->id,
            'product' => new Product($this->productOffer->product),
            'promotions' => Promotion::collection($this->promotions ?? collect()),
            /**
             * The notes that a user has added to the item.
             */
            'notes' => $this->notes,
        ];
    }
}
