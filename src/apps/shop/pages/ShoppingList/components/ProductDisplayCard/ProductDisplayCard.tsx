import { ProductType } from '@/types';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import { StockStatusIcon } from '@/libs/products/components/StockStatusIcon/StockStatusIcon';
import dayjs from 'dayjs';

export interface ProductDisplayCardProps {
  product: ProductType;
  selectedOfferId?: string;
  className?: string;
}

export const ProductDisplayCard = ({
  product,
  selectedOfferId,
  className = '',
}: ProductDisplayCardProps) => {
  // Get the selected offer or the first available offer
  const selectedOffer = selectedOfferId
    ? product.offers.find((offer) => offer.id === selectedOfferId)
    : product.offers[0];

  if (!selectedOffer) {
    return null;
  }

  const { name, imageUrl } = product;
  const { vendorSku, vendor, stockStatus, lastOrderedAt } = selectedOffer;

  return (
    <div
      className={`rounded-lg border border-blue-300 bg-blue-50/30 p-4 ${className}`}
    >
      <div className="flex items-center gap-4">
        {/* Product Image */}
        <div className="flex h-16 w-16 flex-shrink-0 items-center justify-center rounded border border-gray-200 bg-white">
          <img
            src={imageUrl || defaultProductImgUrl}
            alt={name}
            className="h-12 w-12 object-scale-down"
          />
        </div>

        {/* Product Details */}
        <div className="min-w-0 flex-1">
          {/* Stock Status */}
          <div className="mb-1 flex items-center gap-2">
            <StockStatusIcon stockStatus={stockStatus} />
            <span className="text-xs font-medium text-green-600 uppercase">
              {stockStatus === 'IN_STOCK'
                ? 'IN STOCK'
                : stockStatus.replace('_', ' ')}
            </span>
          </div>

          {/* Product Name */}
          <h3 className="mb-1 line-clamp-2 text-base font-semibold text-gray-900">
            {name}
          </h3>

          {/* Product Info */}
          <div className="flex items-center gap-4 text-sm text-gray-600">
            <span>
              <span className="font-medium">SKU:</span> {vendorSku}
            </span>
            <span>{vendor.name}</span>
            {lastOrderedAt && (
              <div className="flex items-center gap-1">
                <svg
                  className="h-4 w-4"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <span>{dayjs(lastOrderedAt).format('M on MM/DD/YY')}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
