import { useState } from 'react';
import { Checkbox } from '@/libs/form/Checkbox';
import { Input } from '@/libs/form/Input';
import { Button } from '@/libs/ui/Button/Button';
import { CustomList } from './components/CustomList/CustomList';
import { ProductType } from '@/types';
import { useListFiltering } from './hooks/useListFiltering';
import mockData from './mockData.json';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { MODAL_NAME } from '@/constants';

type Props = {
  setActiveTab: (tab: number) => void;
};

const productsData: ProductType[] = mockData.productsData.map((product) => ({
  ...product,
  offers: product.offers.map((offer) => ({
    ...offer,
    vendor: {
      ...offer.vendor,
      type: offer.vendor.type as 'manufacturer' | 'distributor',
    },
    stockStatus: offer.stockStatus as 'IN_STOCK' | 'BACKORDER' | 'OUT_OF_STOCK',
    product: null as unknown as ProductType,
  })),
}));
productsData.forEach((product) => {
  product.offers.forEach((offer) => {
    offer.product = product;
  });
});

const mockCustomLists = mockData.mockCustomLists.map((list) => ({
  name: list.name,
  createdBy: 'Lima Neto',
  lastUpdated: '02/02/2025',
  products: list.productIds.map(
    (id) => productsData.find((product) => product.id === id)!,
  ),
}));

export const CustomLists = ({ setActiveTab }: Props) => {
  const { openModal } = useModalStore.getState();
  const [searchQuery, setSearchQuery] = useState('');
  const { filteredLists } = useListFiltering({
    lists: mockCustomLists,
    searchQuery,
  });

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
  };

  const handleTestModal = () => {
    // Use the first product from mock data for testing
    const testProduct = productsData[0];
    if (testProduct) {
      openModal({
        name: MODAL_NAME.ADD_PRODUCT_TO_LIST,
        product: testProduct,
        selectedOfferId: testProduct.offers[0]?.id,
      });
    }
  };

  return (
    <div>
      <div className="mt-4 mb-6 flex items-center justify-between">
        <Checkbox
          checked={false}
          onChange={() => {}}
          label="Select all your lists"
        />
        <div className="flex items-center gap-4">
          <Button
            onClick={handleTestModal}
            variant="secondary"
            size="sm"
            fullWidth={false}
          >
            Test Add Product Modal
          </Button>
          <form onSubmit={handleSearchSubmit}>
            <Input
              placeholder="Search lists or item"
              value={searchQuery}
              onChange={(e) =>
                setSearchQuery(e.target.value.toLocaleLowerCase())
              }
              size="sm"
              className="w-64"
            />
          </form>
        </div>
      </div>
      <div className="space-y-4">
        {filteredLists
          .filter((list) => list.isVisible)
          .map((list, index) => (
            <CustomList
              key={`${list.name}-${index}`}
              setActiveTab={setActiveTab}
              list={list}
              searchQuery={searchQuery}
            />
          ))}
      </div>
    </div>
  );
};
