<?php

declare(strict_types=1);

namespace App\Modules\Integration\Observers;

use App\Modules\Integration\Enums\IntegrationConnectionStatus;
use App\Modules\Integration\Mails\NotifyIntegrationDisconnected;
use App\Modules\Integration\Mails\NotifyMwiConnectionCreated;
use App\Modules\Integration\Models\IntegrationConnection;
use Illuminate\Support\Facades\Mail;

final class IntegrationConnectionObserver
{
    public function created(IntegrationConnection $integrationConnection): void
    {
        // Send notification email for MWI connections
        if ($integrationConnection->vendor->key === 'MWIX') {
            $notificationEmail = config('highfive.mwi.api_access_request_email');

            if ($notificationEmail) {
                Mail::to($notificationEmail)
                    ->cc(config('highfive.mwi.api_access_request_email_bcc'))
                    ->queue(new NotifyMwiConnectionCreated($integrationConnection));
            }
        }
    }

    public function updated(IntegrationConnection $integrationConnection): void
    {
        if ($integrationConnection->status === IntegrationConnectionStatus::Disconnected) {
            $users = $integrationConnection->clinic->account->users;

            if ($users->isEmpty()) {
                return;
            }

            Mail::to('<EMAIL>')->queue(new NotifyIntegrationDisconnected($integrationConnection));
            /*foreach ($users as $user) {
                Mail::to($user->email)->queue(new NotifyIntegrationDisconnected($integrationConnection));
            }*/
        }
    }
}
