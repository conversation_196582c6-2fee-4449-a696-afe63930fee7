<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Actions;

use App\Enums\OrderItemStatus;
use App\Models\OrderItem;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Gpo\Services\VendorCsvExportService;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

final class ExportVendorOrderItemsCsv
{
    public function __construct(
        private readonly VendorCsvExportService $csvExportService,
        private readonly GetVendorsOverview $getVendorsOverview
    ) {}

    public function handle(string $gpoAccountId, Carbon $startDate, Carbon $endDate): BinaryFileResponse
    {
        $vendorsOverview = $this->getVendorsOverview->handle($gpoAccountId, $startDate, $endDate);

        return $this->csvExportService->exportToCsv($vendorsOverview);
    }
}
