<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Modules\Gpo\Http\Requests\SpendAnalysisRequest;
use App\Modules\Gpo\Services\SpendAnalysisCsvExportService;
use App\Modules\Gpo\Services\SpendAnalysisService;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\StreamedResponse;

final class SpendAnalysisController extends Controller
{
    public function __construct(
        private readonly SpendAnalysisService $spendAnalysisService,
        private readonly SpendAnalysisCsvExportService $csvExportService
    ) {}

    /**
     * Get GPO spend analysis data with comprehensive filtering and sorting options
     *
     * Filters supported:
     * - Clinic Info: fulltime_dvm_min/max, total_exam_rooms_min/max, practice_types[]
     * - Total Spend: min_spend, max_spend
     * - Preferred Vendor %: min_preferred_vendor_percent, max_preferred_vendor_percent
     * - Orders: min_orders, max_orders
     * - Inactive Clinics: inactive_only boolean
     * - Date Range: date_from, date_to, quarter, year
     * - Clinic IDs: clinic_ids[]
     *
     * Order By options: name, active_users, inactive_users, notifications, total_spend, preferred_vendor_percent
     */
    public function index(SpendAnalysisRequest $request): JsonResponse
    {
        $gpoAccount = $request->user('gpo')->account;

        $clinics = $this->spendAnalysisService->getClinicsWithAnalytics($gpoAccount->id, $request);

        return response()->json([
            'data' => $clinics->items(),
            'pagination' => [
                'current_page' => $clinics->currentPage(),
                'per_page' => $clinics->perPage(),
                'total' => $clinics->total(),
                'last_page' => $clinics->lastPage(),
            ],
        ]);
    }

    public function summary(SpendAnalysisRequest $request): JsonResponse
    {
        $gpoAccount = $request->user('gpo')->account;
        $summary = $this->spendAnalysisService->getSpendAnalyticsSummary($gpoAccount->id, $request);

        return response()->json($summary);
    }

    /**
     * Export GPO spend analysis data as CSV
     */
    public function export(SpendAnalysisRequest $request): StreamedResponse
    {
        $gpoAccount = $request->user('gpo')->account;

        return $this->csvExportService->exportToCsv($gpoAccount->id, $request);
    }
}
