<?php

declare(strict_types=1);

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\BelongsTo;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\ID;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class ProductAlternative extends Resource
{
    public static $model = \App\Models\ProductAlternative::class;

    public static $title = 'id';

    public static $search = [
        'product.name',
        'product.sku',
        'alternative.name',
        'alternative.sku',
    ];

    public static function label(): string
    {
        return 'Product Alternatives';
    }

    public static function singularLabel(): string
    {
        return 'Product Alternative';
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Product', 'product', Product::class)
                ->required()
                ->searchable()
                ->withSubtitles()
                ->help('The product that has the alternative'),

            BelongsTo::make('Alternative Product', 'alternative', Product::class)
                ->required()
                ->searchable()
                ->withSubtitles()
                ->help('The alternative product'),

            DateTime::make('Created At')
                ->sortable()
                ->exceptOnForms(),

            DateTime::make('Updated At')
                ->sortable()
                ->exceptOnForms(),
        ];
    }

    public function cards(NovaRequest $request): array
    {
        return [];
    }

    public function filters(NovaRequest $request): array
    {
        return [];
    }

    public function lenses(NovaRequest $request): array
    {
        return [];
    }

    public function actions(NovaRequest $request): array
    {
        return [];
    }
}
