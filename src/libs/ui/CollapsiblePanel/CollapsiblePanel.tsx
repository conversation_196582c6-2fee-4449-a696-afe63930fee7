import { AccordionChevron, Collapse, Group } from '@mantine/core';
import { ReactNode } from 'react';
import { useDisclosure } from '@mantine/hooks';
import { cva, type VariantProps } from 'class-variance-authority';
import { mergeClasses } from '@/utils';

const collapsiblePanelVariants = cva('w-full overflow-hidden rounded border', {
  variants: {
    variant: {
      default: 'border-black/[0.05]',
      clean: 'border-black/[0.05]',
      outlined: 'border-gray-300',
      subtle: 'border-gray-200',
      white: 'border-gray-200',
    },
  },
  defaultVariants: {
    variant: 'default',
  },
});

const collapsibleHeaderVariants = cva(
  'relative flex min-h-[2.6rem] w-full items-center',
  {
    variants: {
      variant: {
        default: 'bg-black/[0.03]',
        clean: 'bg-transparent',
        outlined: 'bg-gray-50',
        subtle: 'bg-gray-25',
        white: 'bg-white',
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  },
);

const collapsibleButtonVariants = cva([
  'flex h-6 w-6 items-center justify-center rounded-full p-0',
  'absolute top-1/2 right-4 border border-black/[0.05] bg-white',
  'cursor-pointer transition-transform duration-300',
  '-translate-y-1/2',
]);

interface AccordionProps extends VariantProps<typeof collapsiblePanelVariants> {
  header: ReactNode;
  content: ReactNode;
  startOpen?: boolean;
  className?: string;
}

export const CollapsiblePanel = ({
  header,
  content,
  startOpen = false,
  variant = 'default',
  className,
}: AccordionProps) => {
  const [opened, { toggle }] = useDisclosure(startOpen);

  return (
    <div
      className={mergeClasses(collapsiblePanelVariants({ variant }), className)}
    >
      <Group>
        <div className={collapsibleHeaderVariants({ variant })}>
          {header}
          {content ? (
            <button
              className={mergeClasses(
                collapsibleButtonVariants(),
                opened && '-rotate-180',
              )}
              onClick={toggle}
              data-open={opened}
            >
              <AccordionChevron />
            </button>
          ) : null}
        </div>
      </Group>

      <Collapse in={opened}>{content}</Collapse>
    </div>
  );
};
