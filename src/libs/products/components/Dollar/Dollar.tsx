import { Icon } from '@/libs/icons/Icon';
import { Badge } from '@/libs/ui/Badge/Badge';
import { Gradient } from '@/libs/ui/Gradient/Gradient';
import { Tooltip } from '@/libs/ui/Tooltip';

type DollarProps = {
  toolTipLabel: string;
  size?: string | number;
};

export const Dollar = ({ toolTipLabel, size }: DollarProps) => {
  return (
    <Tooltip label={toolTipLabel}>
      <div>
        <Gradient>
          <Badge className="p-0.5">
            <Icon name="dollar" size={size} aria-label="Dollar sign" />
          </Badge>
        </Gradient>
      </div>
    </Tooltip>
  );
};
