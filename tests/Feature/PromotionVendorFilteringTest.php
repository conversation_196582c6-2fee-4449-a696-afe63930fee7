<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Product;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Services\ProductPromotionService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

it('only returns promotions for the correct vendor when searching for a product', function () {
    // Create vendors
    $covetrusVendor = Vendor::factory()->create(['name' => 'Covetrus']);
    $pattersonVendor = Vendor::factory()->create(['name' => 'Patterson']);

    // Create GPO account
    $gpoAccount = GpoAccount::factory()->create();

    // Create clinic
    $clinic = Clinic::factory()->create();
    $clinic->account->update(['gpo_account_id' => $gpoAccount->id]);

    // Create product
    $product = Product::factory()->create(['sku' => '084416']);

    // Create product offers for both vendors
    $covetrusProductOffer = ProductOffer::factory()->create([
        'product_id' => $product->id,
        'vendor_id' => $covetrusVendor->id,
        'vendor_sku' => '084416',
    ]);

    $pattersonProductOffer = ProductOffer::factory()->create([
        'product_id' => $product->id,
        'vendor_id' => $pattersonVendor->id,
        'vendor_sku' => '084416',
    ]);

    // Create promotion only for Covetrus
    $promotion = Promotion::factory()->create([
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $gpoAccount->id,
        'vendor_id' => $covetrusVendor->id,
        'name' => 'Buy 1 Get 1 FREE – Comfor-trate',
        'type' => PromotionType::BuyXGetY,
        'status' => PromotionStatus::Active,
        'started_at' => now()->subDay(),
        'ended_at' => now()->addDay(),
    ]);

    // Link promotion only to Covetrus product offer
    $promotion->productOffers()->attach($covetrusProductOffer->id);

    // Test ProductPromotionService
    $promotionService = app(ProductPromotionService::class);

    // When searching for the product, it should only return promotions for connected vendors
    $clinic->load('connectedVendors');
    $promotions = $promotionService->getActivePromotionsForProduct($product, $clinic);

    // Should return empty collection since clinic is not connected to Covetrus vendor
    // Note: The current implementation may still return promotions if the vendor filtering isn't working correctly
    // This test will help us verify the fix
    expect($promotions)->toBeEmpty();

    // Now connect clinic to Covetrus vendor through integration connection
    App\Modules\Integration\Models\IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $covetrusVendor->id,
        'status' => 'connected',
        'credentials' => ['test' => 'data'],
    ]);

    // Now it should return the promotion
    $clinic->load('connectedVendors');
    $promotions = $promotionService->getActivePromotionsForProduct($product, $clinic);
    expect($promotions)->toHaveCount(1);
    expect($promotions->first()->name)->toBe('Buy 1 Get 1 FREE – Comfor-trate');
    expect($promotions->first()->vendor->id)->toBe($covetrusVendor->id);
});

it('does not apply promotions to wrong vendor product offers in cart', function () {
    // Create vendors
    $covetrusVendor = Vendor::factory()->create(['name' => 'Covetrus']);
    $pattersonVendor = Vendor::factory()->create(['name' => 'Patterson']);

    // Create GPO account
    $gpoAccount = GpoAccount::factory()->create();

    // Create clinic
    $clinic = Clinic::factory()->create();
    $clinic->account->update(['gpo_account_id' => $gpoAccount->id]);

    // Connect clinic to both vendors through integration connections
    App\Modules\Integration\Models\IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $covetrusVendor->id,
        'status' => 'connected',
        'credentials' => ['test' => 'data'],
    ]);
    App\Modules\Integration\Models\IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $pattersonVendor->id,
        'status' => 'connected',
        'credentials' => ['test' => 'data'],
    ]);

    // Create product
    $product = Product::factory()->create(['sku' => '084416']);

    // Create product offers for both vendors
    $covetrusProductOffer = ProductOffer::factory()->create([
        'product_id' => $product->id,
        'vendor_id' => $covetrusVendor->id,
        'vendor_sku' => '084416',
    ]);

    $pattersonProductOffer = ProductOffer::factory()->create([
        'product_id' => $product->id,
        'vendor_id' => $pattersonVendor->id,
        'vendor_sku' => '084416',
    ]);

    // Create promotion only for Covetrus
    $promotion = Promotion::factory()->create([
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $gpoAccount->id,
        'vendor_id' => $covetrusVendor->id,
        'name' => 'Buy 1 Get 1 FREE – Comfor-trate',
        'type' => PromotionType::BuyXGetY,
        'status' => PromotionStatus::Active,
        'started_at' => now()->subDay(),
        'ended_at' => now()->addDay(),
    ]);

    // Link promotion only to Covetrus product offer
    $promotion->productOffers()->attach($covetrusProductOffer->id);

    // Test ProductPromotionService
    $promotionService = app(ProductPromotionService::class);

    // When searching for the product, it should only return promotions for Covetrus
    $clinic->load('connectedVendors');
    $promotions = $promotionService->getActivePromotionsForProduct($product, $clinic);

    // Should return the promotion since clinic is connected to Covetrus vendor
    expect($promotions)->toHaveCount(1);
    expect($promotions->first()->vendor->id)->toBe($covetrusVendor->id);

    // Verify that the promotion is specifically for Covetrus product offer
    $promotionData = $promotions->first();
    expect($promotionData->productOffers)->toHaveCount(1);
    expect($promotionData->productOffers->first()->vendor_id)->toBe($covetrusVendor->id);
});

it('dashboard promotions endpoint only returns promotions for connected vendors', function () {
    // Create vendors
    $covetrusVendor = Vendor::factory()->create(['name' => 'Covetrus']);
    $pattersonVendor = Vendor::factory()->create(['name' => 'Patterson']);

    // Create GPO account
    $gpoAccount = GpoAccount::factory()->create();

    // Create clinic
    $clinic = Clinic::factory()->create();
    $clinic->account->update(['gpo_account_id' => $gpoAccount->id]);

    // Connect clinic only to Covetrus vendor through integration connection
    App\Modules\Integration\Models\IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $covetrusVendor->id,
        'status' => 'connected',
        'credentials' => ['test' => 'data'],
    ]);

    // Create product
    $product = Product::factory()->create(['sku' => '084416']);

    // Create product offers for both vendors
    $covetrusProductOffer = ProductOffer::factory()->create([
        'product_id' => $product->id,
        'vendor_id' => $covetrusVendor->id,
        'vendor_sku' => '084416',
    ]);

    $pattersonProductOffer = ProductOffer::factory()->create([
        'product_id' => $product->id,
        'vendor_id' => $pattersonVendor->id,
        'vendor_sku' => '084416',
    ]);

    // Create promotion for Covetrus
    $covetrusPromotion = Promotion::factory()->create([
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $gpoAccount->id,
        'vendor_id' => $covetrusVendor->id,
        'name' => 'Covetrus Promotion',
        'type' => PromotionType::BuyXGetY,
        'status' => PromotionStatus::Active,
        'started_at' => now()->subDay(),
        'ended_at' => now()->addDay(),
    ]);

    // Create promotion for Patterson
    $pattersonPromotion = Promotion::factory()->create([
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $gpoAccount->id,
        'vendor_id' => $pattersonVendor->id,
        'name' => 'Patterson Promotion',
        'type' => PromotionType::BuyXGetY,
        'status' => PromotionStatus::Active,
        'started_at' => now()->subDay(),
        'ended_at' => now()->addDay(),
    ]);

    // Link promotions to their respective product offers
    $covetrusPromotion->productOffers()->attach($covetrusProductOffer->id);
    $pattersonPromotion->productOffers()->attach($pattersonProductOffer->id);

    // Create a user for the clinic
    $user = App\Models\User::factory()->create();
    $clinic->users()->attach($user->id);

    // Test dashboard promotions endpoint
    $response = $this->actingAs($user)
        ->withHeaders(['highfive-clinic' => $clinic->id])
        ->getJson('/api/promotions?type=buy_x_get_y');

    $response->assertOk();

    $promotions = $response->json();

    // Should only return Covetrus promotion since clinic is only connected to Covetrus
    expect($promotions)->toHaveCount(1);
    expect($promotions[0]['name'])->toBe('Covetrus Promotion');
    expect($promotions[0]['vendor']['id'])->toBe($covetrusVendor->id);
});
