import { useTranslation } from 'react-i18next';
import { Payables, Onboarding } from '@monite/sdk-react';
import { useMoniteToken } from '@/libs/monite/hooks/useMoniteToken';
import { useMoniteOnboardingData } from '@/libs/monite/hooks/useMoniteOnboardingStatus';
import { Button } from '@/components/atoms/Button/Button';
import { Loader } from '@/libs/ui/Loader/Loader';
import { useAccountStore } from '@/apps/shop/stores/useAccountStore';
import { MoniteProvider } from '@/libs/monite/components/MoniteProvider/MoniteProvider';
import { useEffect } from 'react';

export const Invoices = () => {
  const { t } = useTranslation();
  const activeClinic = useAccountStore((state) => state.activeClinic);

  const { token, isLoading, hasError, handleFetchToken, fetchToken, error } =
    useMoniteToken({
      clinicId: activeClinic?.id || '',
    });

  const {
    isOnboardingComplete,
    verificationStatus,
    isLoading: isLoadingOnboarding,
    refetch: refetchOnboardingStatus,
  } = useMoniteOnboardingData(activeClinic?.id);

  // Refetch onboarding status when returning to the page (after onboarding)
  useEffect(() => {
    if (!isLoadingOnboarding && token) {
      refetchOnboardingStatus();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [token]);

  const handleRetry = () => {
    handleFetchToken();
  };

  // Show message if no clinic is selected
  if (!activeClinic?.id) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="max-w-md text-center">
          <h3 className="mb-2 text-lg font-semibold text-gray-600">
            {t('client.invoices.noClinic.title')}
          </h3>
          <p className="mb-4 text-gray-600">
            {t('client.invoices.noClinic.message')}
          </p>
        </div>
      </div>
    );
  }

  if (isLoading || isLoadingOnboarding) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <Loader size="3rem" />
          <p className="mt-4 text-gray-600">{t('client.invoices.loading')}</p>
        </div>
      </div>
    );
  }

  if (hasError || error) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="max-w-md text-center">
          <h3 className="mb-2 text-lg font-semibold text-red-600">
            {t('client.invoices.error.title')}
          </h3>
          <p className="mb-4 text-gray-600">
            {error || t('client.invoices.error.message')}
          </p>
          <Button onClick={handleRetry} variant="primary">
            {t('client.invoices.error.retry')}
          </Button>
        </div>
      </div>
    );
  }

  if (!token) {
    return (
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="text-center">
          <Button onClick={handleRetry} variant="primary">
            {t('client.invoices.load')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <MoniteProvider
        token={token}
        fetchToken={fetchToken}
        clinicId={activeClinic.id}
      >
        {!isOnboardingComplete ? (
          <div>
            <div className="mb-6">
              <h2 className="text-2xl font-semibold text-gray-800">
                {t('client.invoices.onboarding.title')}
              </h2>
              <p className="mt-2 text-gray-600">
                {t('client.invoices.onboarding.description')}
              </p>
              {verificationStatus && (
                <div
                  className={`mt-2 inline-flex items-center rounded-full px-3 py-1 text-sm font-medium ${
                    verificationStatus === 'enabled'
                      ? 'bg-green-100 text-green-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }`}
                >
                  {t('client.invoices.onboarding.status')}: {verificationStatus}
                </div>
              )}
            </div>
            <Onboarding />
          </div>
        ) : (
          <Payables />
        )}
      </MoniteProvider>
    </div>
  );
};
