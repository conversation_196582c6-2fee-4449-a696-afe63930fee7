import {
  type ModalOptionProps,
  useModalStore,
} from '@/apps/shop/stores/useModalStore';
import { VendorType } from '@/types';
import { Image, Text } from '@mantine/core';
import { Modal } from '@/components';
import defaultProductImgUrl from '@/assets/images/default-product.png';
import styles from './VendorConnectModal.module.css';
import { MODAL_NAME } from '@/constants';
import { CLINIC_UNIFIED_VENDOR_FORM_LINK } from './constants';
import { Input } from '@/libs/form/Input';
import { useForm } from 'react-hook-form';
import * as Yup from 'yup';
import i18n from '@/libs/i18n';
import { useAsyncRequest } from '@/libs/api/hooks/useAsyncRequest/useAsyncRequest';
import { yupResolver } from '@hookform/resolvers/yup';
import { useVendorsStore } from '@/apps/shop/stores/useVendorsStore/useVendorsStore';
import { Button } from '@/libs/ui/Button/Button';

type VendorConnectModalOptions = ModalOptionProps & {
  vendor: VendorType;
};

type VendorConnectForm = {
  username: string;
  password: string;
};

const SCHEMA = Yup.object().shape({
  username: Yup.string()
    .trim()
    .required(
      i18n.t('form.errorMessage.required', {
        path: i18n.t('client.vendors.username'),
      }),
    ),
  password: Yup.string()
    .trim()
    .required(
      i18n.t('form.errorMessage.required', {
        path: i18n.t('client.vendors.password'),
      }),
    ),
});

export const VendorConnectModal = () => {
  const { modalOption, closeModal } = useModalStore();
  const { vendor } = modalOption as VendorConnectModalOptions;
  const { connectVendor } = useVendorsStore();
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<VendorConnectForm>({
    resolver: yupResolver(SCHEMA),
  });

  const { apiRequest: handleConnect, isLoading } = useAsyncRequest({
    apiFunc: handleSubmit(async (values) => {
      await connectVendor({ vendorId: vendor.id, ...values });

      reset();
      closeModal();
    }),
  });

  if (!vendor) {
    return null;
  }

  const isReconnect = Boolean(vendor.lastProductCatalogSync);
  const buttonLabel = isReconnect ? 'Update' : 'Connect';

  return (
    <Modal
      name={MODAL_NAME.VENDOR_CONNECT}
      size="md"
      customClasses={{
        header: styles.outerModalHeader,
        body: styles.outerModalBody,
        content: styles.outerModalContent,
      }}
      withCloseButton
    >
      <div className={styles.modal}>
        <div className={styles.modalContent}>
          <Image
            src={vendor.imageUrl}
            alt={vendor.name}
            fallbackSrc={defaultProductImgUrl}
            className={styles.vendorLogo}
            w="130px"
          />
          <form onSubmit={handleConnect} className="w-full">
            <div className="mb-4">
              <Input
                label="Username"
                {...register('username')}
                error={errors.username?.message}
              />
            </div>
            <div className="mb-4">
              <Input
                label="Password"
                {...register('password')}
                error={errors.password?.message}
                autoComplete="off"
              />
            </div>
            <Button loading={isLoading}>{buttonLabel}</Button>
          </form>
          {!isReconnect && (
            <Text c="#222" size="14px" mt="24px">
              Don’t have an account with this vendor?{' '}
              <a
                href={CLINIC_UNIFIED_VENDOR_FORM_LINK}
                rel="noreferrer"
                target="_blank"
                style={{ textDecoration: 'none' }}
              >
                <Text c="#0072C6" fw="500" size="14px" span>
                  Create account.
                </Text>
              </a>
            </Text>
          )}
        </div>
      </div>
    </Modal>
  );
};
