# Monite Integration Module

Laravel module for integrating with the Monite API for financial management in veterinary clinics.

## 🎯 What It Does

- **Entity Management**: Create/manage Monite entities for clinics
- **User Sync**: Sync clinic users with Monite
- **Role Mapping**: Map clinic roles to Monite permissions
- **Vendor Sync**: Sync vendors to Monite counterparts
- **Payables**: Convert invoices to Monite payables
- **Webhook Handling**: Process Monite webhook events for payables
- **Invoice Matching**: Match Monite payables with local invoices
- **Feature Flags**: Control Monite features per clinic
- **Token Management**: OAuth2 token handling with caching
- **Error Handling**: Comprehensive error handling and logging

## ⚙️ Setup

Add to `.env`:
```env
MONITE_ENVIRONMENT=sandbox
MONITE_CLIENT_ID=your_client_id
MONITE_CLIENT_SECRET=your_client_secret

# Webhook secrets (one per object type)
MONITE_WEBHOOK_SECRET_PAYABLE=whsec_your_payable_secret
MONITE_WEBHOOK_SECRET_COUNTERPART=whsec_your_counterpart_secret
```

## 🖥️ Artisan Commands

### `monite:sync-missing`
Comprehensive sync command for entities, roles, and users.

```bash
# Check sync status
php artisan monite:sync-missing --status

# Sync all missing entities/users
php artisan monite:sync-missing

# Sync specific clinic
php artisan monite:sync-missing --clinic-id=123

# Preview changes (dry run)
php artisan monite:sync-missing --dry-run

# Use queue for processing
php artisan monite:sync-missing --queue
```

### `monite:sync-users`
Sync clinic users with Monite entity users.

```bash
# Sync users for all clinics
php artisan monite:sync-users

# Sync users for specific clinic
php artisan monite:sync-users --clinic-id=123

# Use queue for processing
php artisan monite:sync-users --queue
```

### `monite:sync-roles`
Sync clinic roles with Monite entity roles.

```bash
# Sync roles for all clinics
php artisan monite:sync-roles

# Sync roles for specific clinic
php artisan monite:sync-roles --clinic-id=123

# Use queue for processing
php artisan monite:sync-roles --queue
```

### `monite:sync-counterparts`
Sync vendors to Monite counterparts.

```bash
# Sync counterparts for all clinics
php artisan monite:sync-counterparts

# Sync counterparts for specific clinic
php artisan monite:sync-counterparts --clinic-id=123

# Use queue for processing
php artisan monite:sync-counterparts --queue
```

### `monite:process-payables`
Convert invoices to Monite payables.

```bash
# Process payables for all clinics
php artisan monite:process-payables

# Process payables for specific clinic
php artisan monite:process-payables --clinic-id=123
```

**Common Options:**
- `--clinic-id=ID` - Process for specific clinic
- `--queue` - Use background queue processing

## 🔔 Webhooks

Monite webhooks provide real-time notifications about events in the Monite system. The module handles these webhooks to keep local data in sync with authoritative Monite data.

### Setup

1. **Configure Secrets**: Set webhook secrets for each object type in your `.env` file:
   ```env
   MONITE_WEBHOOK_SECRET_PAYABLE=whsec_your_payable_secret
   MONITE_WEBHOOK_SECRET_COUNTERPART=whsec_your_counterpart_secret
   ```
2. **Endpoint URL**: Your webhook endpoint is available at `/api/webhooks/monite`
3. **Register in Monite**: Register this URL in the Monite Partner Portal or via API
4. **Rate Limiting**: Endpoint is rate-limited to 100 requests per minute per IP

### Supported Webhooks

- `payable.created` - When a new payable is created in Monite
- `payable.updated` - When an existing payable is updated in Monite
- `payable.ocr_finished` - When OCR processing is completed for a payable

### Webhook Processing

**Signature Verification:**
- HMAC-SHA256 signature validation using object-type-specific secrets
- Timestamp validation (5-minute tolerance)
- Automatic bypass in testing environments when secrets are not configured

**Event Storage:**
- All webhook events are stored in `monite_webhook_events` table
- Idempotency handling prevents duplicate processing
- Status tracking using `MoniteWebhookEventStatus` enum

**Payable-Invoice Matching:**
When a payable webhook is received:
1. **Fetch Full Data**: Retrieves complete payable details from Monite API
2. **Match by ID**: Looks for existing invoice by `monite_payable_id` and `clinic_id`
3. **Update or Create**: 
   - If match found: Updates existing invoice with authoritative Monite data
   - If no match: Creates new generic invoice with Monite data
4. **Data Source**: Uses Monite as single source of truth (no fallbacks)

**Data Mapping:**
- `total_amount` → `amount` (converted from cents to dollars)
- `document_id` → `invoice_number`
- `counterpart_raw_data.name` → `vendor_name`
- `status` → `status`
- `currency` → `currency`
- `due_date` → `due_date`

## 🏗️ Architecture

### Base Command Class
All Monite commands extend `BaseMoniteCommand` which provides:
- Common clinic validation logic
- Feature flag checking
- Error handling patterns
- Consistent output formatting

### Service Layer
- **MoniteApiClient**: HTTP client for Monite API
- **MoniteTokenManager**: OAuth2 token management
- **MoniteSyncService**: Main sync orchestration
- **MoniteEntityService**: Entity creation/management
- **MoniteUserService**: User sync operations
- **MoniteRoleService**: Role sync operations
- **MoniteCounterpartService**: Vendor sync operations
- **MonitePayableService**: Invoice to payable conversion
- **MonitePayableProcessor**: Process payables and create/update invoices
- **MoniteWebhookVerificationService**: Verify webhook signatures
- **MoniteFeatureFlagService**: Feature flag management

### Action Layer
- **CreateOrUpdateEntityAction**: Entity creation logic
- **SyncUsersAction**: User sync logic
- **SyncRolesAction**: Role sync logic
- **CreatePayableFromInvoiceAction**: Payable creation logic
- **ProcessPayableWebhookAction**: Process payable webhook events
- **DeleteEntityAction**: Entity deletion logic

### Job Layer
All operations can be queued for background processing:
- **SyncMissingMoniteEntitiesJob**: Complete sync job
- **SyncMoniteUsersJob**: User sync job
- **SyncMoniteRolesJob**: Role sync job
- **SyncAllVendorCounterpartsJob**: Vendor sync job
- **CreateOrUpdateMoniteEntityJob**: Entity creation job
- **CreatePayableFromInvoiceJob**: Payable creation job
- **DeleteMoniteEntityJob**: Entity deletion job

## 🔧 Key Services

### `MoniteSyncService`
Main service for syncing clinics with Monite:
```php
$syncService = app(MoniteSyncService::class);
$results = $syncService->syncClinic($clinic);
```

### `MoniteFeatureFlagService`
Control Monite features per clinic:
```php
$featureService = app(MoniteFeatureFlagService::class);
$featureService->enable($clinic);
$featureService->isEnabled($clinic);
```

### `MoniteApiClient`
HTTP client with retry logic and error handling:
```php
$client = app(MoniteApiClientInterface::class);
$response = $client->get('/entities');
```

### `MoniteTokenManager`
OAuth2 token management with caching:
```php
$tokenManager = app(MoniteTokenManager::class);
$token = $tokenManager->getAccessToken();
```

## 🗄️ Models

### `MoniteUserMapping`
Maps local users to Monite users:
```php
// Fields: clinic_id, user_id, monite_user_id
$mapping = MoniteUserMapping::where('clinic_id', $clinic->id)
    ->where('user_id', $user->id)
    ->first();
```

### `MoniteRoleMapping`
Maps local roles to Monite roles:
```php
// Fields: clinic_id, role_id, monite_role_id
$mapping = MoniteRoleMapping::where('clinic_id', $clinic->id)
    ->where('role_id', $role->id)
    ->first();
```

### `MoniteVendorMapping`
Maps local vendors to Monite counterparts:
```php
// Fields: clinic_id, vendor_id, monite_counterpart_id
$mapping = MoniteVendorMapping::where('clinic_id', $clinic->id)
    ->where('vendor_id', $vendor->id)
    ->first();
```

### `MoniteWebhookEvent`
Stores webhook events from Monite:
```php
// Fields: event_id, event_type, entity_id, object_type, object_id, payload, status, error_message, processed_at
$events = MoniteWebhookEvent::where('event_type', 'payable.created')->get();

// Check event status using enum
$event->markAsProcessed();
$event->markAsFailed('Error message');
$event->markAsIgnored('Unsupported event type');

// Query by status
$pendingEvents = MoniteWebhookEvent::pending()->get();
$failedEvents = MoniteWebhookEvent::failed()->get();
```

### `MoniteInvoice`
Stores invoice data from Monite payables:
```php
// Fields: clinic_id, vendor_id, external_order_id, invoice_number, status, amount, currency, due_date, vendor_name, monite_payable_id
$invoices = MoniteInvoice::where('clinic_id', $clinic->id)->get();

// Find by Monite payable ID
$invoice = MoniteInvoice::where('monite_payable_id', $payableId)->first();

// Relationships
$invoice->clinic; // BelongsTo Clinic
$invoice->vendor; // BelongsTo Vendor (nullable)
$invoice->externalOrder; // BelongsTo ExternalOrder (nullable)
```

## 🚩 Feature Flags

Control Monite per clinic using Laravel Pennant:
```php
$featureService = app(MoniteFeatureFlagService::class);

// Enable/disable for clinic
$featureService->enable($clinic);
$featureService->disable($clinic);

// Check status
$featureService->isEnabled($clinic);
```

## 🔐 Authentication & Security

### OAuth2 Flow
- **Client Credentials**: For backend-to-backend communication
- **Entity User**: For user-specific operations
- **Token Caching**: Automatic token refresh and caching
- **Rate Limiting**: Built-in rate limiting with retry logic

### Error Handling
- **MoniteApiException**: Custom exception for API errors
- **Retry Logic**: Automatic retry for transient failures
- **Logging**: Comprehensive error logging
- **Graceful Degradation**: Commands continue on individual failures

## 📊 Data Transfer Objects

### `MoniteApiResponse`
Structured response handling:
```php
$response = MoniteApiResponse::fromResponse($httpResponse);
$data = $response->getData();
$pagination = $response->getPagination();
```

## 🎛️ Configuration

Configuration in `config/monite.php`:
- **Environments**: Sandbox/Production settings
- **Credentials**: Client ID/Secret
- **API Settings**: Base URLs, version, timeouts
- **Rate Limiting**: Request limits and retry policies
- **Webhook Secrets**: Object-type-specific webhook secrets
- **Role Permissions**: Detailed permission mappings for all user roles

## 🔄 Integration Points

### Nova Actions
- **SetupMoniteIntegration**: One-click Monite setup for clinics

### Traits
- **ChecksMoniteFeatureFlag**: Reusable feature flag checking

### Enums
- **MoniteEnvironment**: Environment management
- **MoniteGrantType**: OAuth2 grant types
- **MoniteWebhookEventStatus**: Webhook event status management
  - `PENDING` - Event received but not yet processed
  - `PROCESSING` - Event currently being processed
  - `PROCESSED` - Event successfully processed
  - `FAILED` - Event processing failed
  - `IGNORED` - Event ignored (e.g., unsupported type)

## ⚠️ Troubleshooting

```bash
# Check sync status
php artisan monite:sync-missing --status

# Preview changes
php artisan monite:sync-missing --dry-run

# Check feature flags
php artisan tinker
>>> app(MoniteFeatureFlagService::class)->isEnabled($clinic)

# Check token status
>>> app(MoniteTokenManager::class)->getAccessToken()

# Test API connection
>>> app(MoniteApiClientInterface::class)->get('/entities')

# Check webhook events
>>> MoniteWebhookEvent::latest()->take(5)->get(['event_id', 'event_type', 'status', 'created_at'])

# Check invoice data
>>> MoniteInvoice::where('clinic_id', $clinic->id)->latest()->take(5)->get()

# Test webhook signature verification
>>> app(MoniteWebhookVerificationService::class)->verifySignature($signature, $payload, 'payable')
```

## 🚀 Usage Examples

### Manual Entity Creation
```php
$entityService = app(MoniteEntityService::class);
$entityId = $entityService->createEntityForClinic($clinic);
```

### User Sync
```php
$userService = app(MoniteUserService::class);
$userService->syncUserForClinic($user, $clinic);
```

### Payable Processing
```php
$payableService = app(MonitePayableService::class);
$count = $payableService->processClinicInvoices($clinic);
```

### Queue Jobs
```php
// Dispatch sync job
SyncMissingMoniteEntitiesJob::dispatch($clinic);

// Dispatch user sync
SyncMoniteUsersJob::dispatch($clinic);
```

### Webhook Testing
```php
// Test webhook processing
$webhookController = app(MoniteWebhookController::class);
$request = Request::create('/api/webhooks/monite', 'POST', $payload, [], [], [
    'HTTP_MONITE_SIGNATURE' => $signature
]);
$response = $webhookController->handle($request);

// Check webhook events
$events = MoniteWebhookEvent::where('event_type', 'payable.created')->get();
$failedEvents = MoniteWebhookEvent::failed()->get();

// Check invoice data
$invoices = MoniteInvoice::where('clinic_id', $clinic->id)->get();
$invoice = MoniteInvoice::where('monite_payable_id', $payableId)->first();
```

## 📈 Performance Features

- **Queue Support**: All operations can be queued
- **Batch Processing**: Efficient bulk operations
- **Pagination**: Automatic pagination handling
- **Caching**: Token caching and response caching
- **Rate Limiting**: Built-in API rate limiting
- **Retry Logic**: Automatic retry for failed requests

## 🔍 Monitoring & Logging

- **Comprehensive Logging**: All operations logged
- **Error Tracking**: Detailed error information
- **Performance Metrics**: Request timing and counts
- **Status Reporting**: Command status summaries