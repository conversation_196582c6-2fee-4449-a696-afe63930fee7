import type { <PERSON><PERSON>, <PERSON>Obj } from '@storybook/react-vite';
import { <PERSON>u, <PERSON>uItem, <PERSON>u<PERSON>abel, MenuSeparator } from './Menu';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';

type Story = StoryObj<typeof Menu>;

const meta: Meta<typeof Menu> = {
  title: 'UI/Menu',
  component: Menu,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    size: {
      control: 'select',
      options: ['sm', 'md', 'lg'],
    },
    side: {
      control: 'select',
      options: ['top', 'right', 'bottom', 'left'],
    },
    align: {
      control: 'select',
      options: ['start', 'center', 'end'],
    },
  },
};

export default meta;

export const Default: Story = {
  args: {
    trigger: <Button variant="default">Open Menu</Button>,
    children: (
      <>
        <MenuItem onClick={() => console.log('Edit clicked')}>Edit</MenuItem>
        <MenuItem onClick={() => console.log('Delete clicked')}>
          Delete
        </MenuItem>
        <MenuSeparator />
        <MenuItem onClick={() => console.log('Settings clicked')}>
          Settings
        </MenuItem>
      </>
    ),
  },
};

export const WithIcon: Story = {
  args: {
    trigger: (
      <Button variant="unstyled" aria-label="More options">
        <Icon name="moreOptions" color="#333" size="1.4rem" />
      </Button>
    ),
    children: (
      <>
        <MenuItem onClick={() => console.log('Edit Credentials clicked')}>
          <p className="text-sm font-medium text-black">Edit Credentials</p>
        </MenuItem>
        <MenuItem onClick={() => console.log('Shipping Information clicked')}>
          <p className="text-sm font-medium text-black">Shipping Information</p>
        </MenuItem>
      </>
    ),
  },
};

export const WithLabel: Story = {
  args: {
    trigger: <Button variant="secondary">Actions</Button>,
    children: (
      <>
        <MenuLabel>Account</MenuLabel>
        <MenuItem onClick={() => console.log('Profile clicked')}>
          Profile
        </MenuItem>
        <MenuItem onClick={() => console.log('Settings clicked')}>
          Settings
        </MenuItem>
        <MenuSeparator />
        <MenuLabel>Danger Zone</MenuLabel>
        <MenuItem onClick={() => console.log('Delete clicked')}>
          <span className="text-red-600">Delete Account</span>
        </MenuItem>
      </>
    ),
  },
};

export const Sizes: Story = {
  render: () => (
    <div className="flex gap-4">
      <Menu size="sm" trigger={<Button size="sm">Small Menu</Button>}>
        <MenuItem>Small Item</MenuItem>
        <MenuItem>Another Item</MenuItem>
      </Menu>

      <Menu size="md" trigger={<Button>Medium Menu</Button>}>
        <MenuItem>Medium Item</MenuItem>
        <MenuItem>Another Item</MenuItem>
      </Menu>

      <Menu size="lg" trigger={<Button>Large Menu</Button>}>
        <MenuItem>Large Item</MenuItem>
        <MenuItem>Another Item</MenuItem>
      </Menu>
    </div>
  ),
};

export const Positioning: Story = {
  render: () => (
    <div className="grid grid-cols-2 gap-8 p-8">
      <Menu side="top" align="start" trigger={<Button>Top Start</Button>}>
        <MenuItem>Item 1</MenuItem>
        <MenuItem>Item 2</MenuItem>
      </Menu>

      <Menu side="top" align="end" trigger={<Button>Top End</Button>}>
        <MenuItem>Item 1</MenuItem>
        <MenuItem>Item 2</MenuItem>
      </Menu>

      <Menu side="bottom" align="start" trigger={<Button>Bottom Start</Button>}>
        <MenuItem>Item 1</MenuItem>
        <MenuItem>Item 2</MenuItem>
      </Menu>

      <Menu side="bottom" align="end" trigger={<Button>Bottom End</Button>}>
        <MenuItem>Item 1</MenuItem>
        <MenuItem>Item 2</MenuItem>
      </Menu>
    </div>
  ),
};
