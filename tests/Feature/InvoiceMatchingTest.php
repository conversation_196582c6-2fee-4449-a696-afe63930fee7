<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Order;
use App\Models\SubOrder;
use App\Models\Vendor;
use App\Modules\Monite\Models\MoniteInvoice;
use App\Modules\Order\Actions\MatchInvoiceWithExternalOrderAction;
use App\Modules\Order\Models\ExternalOrder;

it('matches invoice with external order when invoice arrives first', function () {
    $clinic = Clinic::factory()->create();
    $vendor = Vendor::factory()->create();
    $order = Order::factory()->create(['clinic_id' => $clinic->id]);
    $subOrder = SubOrder::factory()->create([
        'order_id' => $order->id,
        'vendor_id' => $vendor->id,
    ]);

    // Create invoice first
    $invoice = MoniteInvoice::factory()->create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $vendor->id,
        'invoice_number' => 'INV-12345',
        'external_order_id' => null,
    ]);

    // Create external order later with matching invoice number
    $externalOrder = ExternalOrder::factory()->create([
        'sub_order_id' => $subOrder->id,
        'invoice_number' => 'INV-12345',
    ]);

    // Execute the matching action
    $action = app(MatchInvoiceWithExternalOrderAction::class);
    $action->execute($externalOrder);

    // The invoice should be matched
    $invoice->refresh();
    expect($invoice->external_order_id)->toBe($externalOrder->id);
});

it('does not match invoices across different vendors', function () {
    $clinic = Clinic::factory()->create();
    $vendor = Vendor::factory()->create();
    $otherVendor = Vendor::factory()->create();
    $order = Order::factory()->create(['clinic_id' => $clinic->id]);
    $subOrder = SubOrder::factory()->create([
        'order_id' => $order->id,
        'vendor_id' => $vendor->id,
    ]);

    // Create invoice for one vendor
    $invoice = MoniteInvoice::factory()->create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $vendor->id,
        'invoice_number' => 'INV-12345',
        'external_order_id' => null,
    ]);

    // Create external order for different vendor with same invoice number
    $otherSubOrder = SubOrder::factory()->create([
        'order_id' => $order->id,
        'vendor_id' => $otherVendor->id,
    ]);

    $externalOrder = ExternalOrder::factory()->create([
        'sub_order_id' => $otherSubOrder->id,
        'invoice_number' => 'INV-12345',
    ]);

    // Execute the matching action
    $action = app(MatchInvoiceWithExternalOrderAction::class);
    $action->execute($externalOrder);

    // They should not be matched
    $invoice->refresh();
    expect($invoice->external_order_id)->toBeNull();
});
