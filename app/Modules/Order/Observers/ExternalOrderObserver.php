<?php

declare(strict_types=1);

namespace App\Modules\Order\Observers;

use App\Modules\Order\Actions\MatchInvoiceWithExternalOrderAction;
use App\Modules\Order\Models\ExternalOrder;

final class ExternalOrderObserver
{
    public function __construct(
        private readonly MatchInvoiceWithExternalOrderAction $matchInvoiceWithExternalOrderAction
    ) {}

    /**
     * Handle the ExternalOrder "created" event.
     */
    public function created(ExternalOrder $externalOrder): void
    {
        //
    }

    /**
     * Handle the ExternalOrder "updated" event.
     */
    public function updated(ExternalOrder $externalOrder): void
    {
        // Check if invoice_number was updated and is not null
        if ($externalOrder->wasChanged('invoice_number') && $externalOrder->invoice_number) {
            $this->matchInvoiceWithExternalOrderAction->execute($externalOrder);
        }
    }

    /**
     * Handle the ExternalOrder "deleted" event.
     */
    public function deleted(ExternalOrder $externalOrder): void
    {
        //
    }

    /**
     * Handle the ExternalOrder "restored" event.
     */
    public function restored(ExternalOrder $externalOrder): void
    {
        //
    }

    /**
     * Handle the ExternalOrder "force deleted" event.
     */
    public function forceDeleted(ExternalOrder $externalOrder): void
    {
        //
    }
}
