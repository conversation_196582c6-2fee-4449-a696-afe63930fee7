<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use App\Models\Vendor;
use Illuminate\Support\Collection;

class VendorFuzzyMatcher
{
    private const MIN_SIMILARITY_THRESHOLD = 0.4;

    /**
     * Find the best vendor match for a Monite counterpart.
     */
    public function findBestMatch(string $counterpartName): ?Vendor
    {
        $vendors = $this->getAllVendors();
        $bestMatch = null;
        $bestScore = 0;

        foreach ($vendors as $vendor) {
            $similarityScore = $this->calculateSimilarity($counterpartName, $vendor->name);

            if ($similarityScore >= self::MIN_SIMILARITY_THRESHOLD && $similarityScore > $bestScore) {
                $bestMatch = $vendor;
                $bestScore = $similarityScore;
            }
        }

        return $bestMatch;
    }

    /**
     * Get all vendors for matching.
     */
    private function getAllVendors(): Collection
    {
        return Vendor::all();
    }

    /**
     * Calculate similarity between two strings using multiple algorithms.
     */
    private function calculateSimilarity(string $string1, string $string2): float
    {
        $string1 = $this->normalizeString($string1);
        $string2 = $this->normalizeString($string2);

        if ($string1 === $string2) {
            return 1.0;
        }

        if (empty($string1) || empty($string2)) {
            return 0.0;
        }

        // Extract first words for brand comparison
        $firstWord1 = explode(' ', $string1)[0];
        $firstWord2 = explode(' ', $string2)[0];

        // If first words match exactly, give very high score
        if ($firstWord1 === $firstWord2) {
            // Perfect first word match - score based on how much of the rest matches
            $rest1 = trim(mb_substr($string1, mb_strlen($firstWord1)));
            $rest2 = trim(mb_substr($string2, mb_strlen($firstWord2)));

            if (empty($rest1) && empty($rest2)) {
                return 1.0; // Both are just the brand name
            }

            // Calculate similarity of the rest
            $restSimilarity = empty($rest1) || empty($rest2) ? 0.5 : $this->levenshteinSimilarity($rest1, $rest2);

            return 0.9 + ($restSimilarity * 0.1); // Range: 0.9-1.0
        }

        // Check if one string contains the other (for cases like "Zoetis Animal Health" vs "Zoetis")
        if (str_contains($string1, $string2) || str_contains($string2, $string1)) {
            $shorter = mb_strlen($string1) < mb_strlen($string2) ? $string1 : $string2;
            $longer = mb_strlen($string1) >= mb_strlen($string2) ? $string1 : $string2;

            // Give higher score for prefix matches (when shorter string is at the beginning)
            if (str_starts_with($longer, $shorter)) {
                return 0.8 + (mb_strlen($shorter) / mb_strlen($longer)) * 0.2; // Range: 0.8-1.0
            }

            // Regular substring match
            return 0.6 + (mb_strlen($shorter) / mb_strlen($longer)) * 0.2; // Range: 0.6-0.8
        }

        return $this->levenshteinSimilarity($string1, $string2);
    }

    /**
     * Normalize string for comparison.
     */
    private function normalizeString(string $string): string
    {
        // Remove common business suffixes and normalize
        $normalized = mb_strtolower(trim($string));
        $normalized = preg_replace('/\b(inc|corp|corporation|ltd|limited|llc|co|company)\b\.?/', '', $normalized);
        $normalized = preg_replace('/[^a-zA-Z0-9\s]/', '', $normalized);
        $normalized = preg_replace('/\s+/', ' ', trim($normalized));

        return $normalized;
    }

    /**
     * Calculate Levenshtein similarity (0-1 scale).
     */
    private function levenshteinSimilarity(string $string1, string $string2): float
    {
        $maxLength = max(mb_strlen($string1), mb_strlen($string2));

        if ($maxLength === 0) {
            return 1.0;
        }

        $distance = levenshtein($string1, $string2);

        return 1 - ($distance / $maxLength);
    }
}
