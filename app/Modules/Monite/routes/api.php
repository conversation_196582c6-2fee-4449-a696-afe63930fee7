<?php

declare(strict_types=1);

use App\Modules\Monite\Http\Controllers\MoniteWebhookController;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Monite API Routes
|--------------------------------------------------------------------------
|
| These routes handle Monite integration endpoints, including webhooks
| and other Monite-related functionality.
|
*/

Route::prefix('api')->group(function () {
    // Webhook endpoint - no auth, verification handled by the controller
    Route::post('webhooks/monite', [MoniteWebhookController::class, 'handle'])
        ->name('monite.webhook.handle');
});
