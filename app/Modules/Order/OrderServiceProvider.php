<?php

declare(strict_types=1);

namespace App\Modules\Order;

use App\Listeners\SendOrderPlacementFailedSlackNotification;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\SubOrder;
use App\Modules\Order\Events\OrderCreated;
use App\Modules\Order\Events\OrderPlacementFailed;
use App\Modules\Order\Events\Vendor\VendorConnectionDisconnected;
use App\Modules\Order\Listeners\InvalidateCartAfterPurchase;
use App\Modules\Order\Listeners\Vendor\MarkConnectionAsDisconnected;
use App\Modules\Order\Models\ExternalOrder;
use App\Modules\Order\Models\ImportOrderHistoryTask;
use App\Modules\Order\Models\ImportOrderItemCsvTask;
use App\Modules\Order\Nova\ImportOrderHistoryTask as ImportOrderHistoryTaskResource;
use App\Modules\Order\Nova\ImportOrderItemCsvTask as ImportOrderItemCsvTaskResource;
use App\Modules\Order\Observers\ExternalOrderObserver;
use App\Modules\Order\Observers\ImportOrderHistoryTaskObserver;
use App\Modules\Order\Observers\ImportOrderItemCsvTaskObserver;
use App\Modules\Order\Policies\OrderItemPolicy;
use App\Modules\Order\Policies\OrderPolicy;
use App\Modules\Order\Policies\SubOrderPolicy;
use App\Modules\Promotion\Listeners\ProcessOrderPromotions;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\ServiceProvider;
use Laravel\Nova\Nova;

final class OrderServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->registerRoutes();
        $this->registerPolicies();
        $this->registerNovaResources();
        $this->registerObservers();

        Event::listen(
            VendorConnectionDisconnected::class,
            [
                MarkConnectionAsDisconnected::class, 'handle',
                // @TODO: add email notification
            ],
        );

        Event::listen(
            OrderPlacementFailed::class,
            [
                SendOrderPlacementFailedSlackNotification::class, 'handle',
                // @TODO: add email notification
            ]
        );

        Event::listen(
            OrderCreated::class,
            [
                ProcessOrderPromotions::class, 'handle',
                InvalidateCartAfterPurchase::class, 'handle',
            ],
        );
    }

    protected function registerRoutes(): void
    {
        $this->loadRoutesFrom(__DIR__.'/routes.php');
    }

    protected function registerPolicies(): void
    {
        $polices = [
            Order::class => OrderPolicy::class,
            OrderItem::class => OrderItemPolicy::class,
            SubOrder::class => SubOrderPolicy::class,
        ];

        foreach ($polices as $model => $policy) {
            Gate::policy($model, $policy);
        }
    }

    protected function registerNovaResources(): void
    {
        Nova::resources([
            ImportOrderHistoryTaskResource::class,
            ImportOrderItemCsvTaskResource::class,
        ]);
    }

    protected function registerObservers(): void
    {
        ImportOrderHistoryTask::observe(ImportOrderHistoryTaskObserver::class);
        ImportOrderItemCsvTask::observe(ImportOrderItemCsvTaskObserver::class);
        ExternalOrder::observe(ExternalOrderObserver::class);
    }
}
