import { ColumnDef, createColumnHelper } from '@tanstack/react-table';
import i18n from '@/libs/i18n';
import { Text } from '@mantine/core';

import { VendorTableType } from './types';
import styles from './dashboard.module.css';

const columnHelper = createColumnHelper<VendorTableType>();

export const COLUMNS = [
  columnHelper.accessor('vendor', {
    header: i18n.t('client.dashboard.columns.vendor'),
    cell: (info) => {
      const vendor = info.getValue();

      return (
        <div className={styles.vendorCell}>
          <img src={vendor.imgUrl} alt={vendor.name} />

          <Text size="sm" fw={700} ml="1rem">
            {vendor.name}
          </Text>
        </div>
      );
    },
  }),
  columnHelper.accessor('spend', {
    header: i18n.t('client.dashboard.columns.spend'),
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('orders', {
    header: i18n.t('client.dashboard.columns.orders'),
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('allOrders', {
    header: i18n.t('client.dashboard.columns.allOrders'),
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('reqSpend', {
    header: i18n.t('client.dashboard.columns.reqSpend'),
    cell: (info) => info.getValue(),
  }),
  columnHelper.accessor('rank', {
    header: i18n.t('client.dashboard.columns.rank'),
    cell: (info) => info.getValue(),
  }),
] as ColumnDef<VendorTableType>[];
