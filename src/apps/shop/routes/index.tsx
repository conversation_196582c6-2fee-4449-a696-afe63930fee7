import { lazy, LazyExoticComponent } from 'react';
import { createBrowserRouter, RouteObject } from 'react-router-dom';

import { AuthLayout } from '@/libs/auth/components/AuthLayout/AuthLayout';
import { ClinicLayout } from '@/apps/shop/Layouts/ClinicLayout/ClinicLayout';

import { SHOP_ROUTES_PATH } from './routes';
import { NavigationHistoryProvider } from '@/libs/utils/navigation/hooks/useNavigationHistory';
import { AccountLayout } from '@/apps/shop/Layouts/AccountLayout/AccountLayout';
import { Home } from '@/apps/shop/pages/Home/Home';
import { SettingsLayout } from '@/apps/shop/Layouts/SettingsLayout/SettingsLayout';
import { MoniteFeatureGuard } from '@/components/guards/MoniteFeatureGuard';

const pages = [
  'AmazonBusiness',
  'Login',
  'ResetPassword',
  'ForgotPassword',
  'ClinicDashboard',
  'Checkout',
  'Search',
  'ProductDetails',
  'OrderHistory',
  'Cart',
  'Vendors',
  'SignUp',
  'Settings',
  'AccountDetails',
  'Dashboard',
  'Page404',
  'ClinicManagement',
  'Invoices',
  'ShoppingList',
] as const;
type PageName = (typeof pages)[number];

const pageModules: Record<
  PageName,
  LazyExoticComponent<() => JSX.Element>
> = pages.reduce(
  (acc, pageName) => ({
    ...acc,
    [pageName]: lazy(() =>
      import(`@/apps/shop/pages/${pageName}/${pageName}.tsx`).then(
        (pageModule) => ({
          default: pageModule[pageName] as React.FC,
        }),
      ),
    ),
  }),
  {} as Record<PageName, LazyExoticComponent<() => JSX.Element>>,
);

export const routes: RouteObject[] = [
  {
    path: SHOP_ROUTES_PATH.home,
    element: <Home />,
  },
  {
    element: <NavigationHistoryProvider />,
    children: [
      {
        path: SHOP_ROUTES_PATH.amazonBusiness,
        element: <pageModules.AmazonBusiness />,
      },
      {
        path: SHOP_ROUTES_PATH.signUp,
        element: <pageModules.SignUp />,
      },
      {
        element: (
          <AuthLayout bg="linear-gradient(90deg, #e9cd6826, #bddcf026, #eac5d726), linear-gradient(90deg, var(--mantine-color-white) 50%, var(--mantine-color-light-blue-3) 100%)" />
        ),
        children: [
          {
            path: SHOP_ROUTES_PATH.login,
            element: <pageModules.Login />,
          },
          {
            path: SHOP_ROUTES_PATH.forgotPassword,
            element: <pageModules.ForgotPassword />,
          },
          {
            path: SHOP_ROUTES_PATH.resetPassword,
            element: <pageModules.ResetPassword />,
          },
        ],
      },
      {
        path: '/',
        element: <ClinicLayout />,
        children: [
          {
            path: SHOP_ROUTES_PATH.clinicDashboard,
            element: <pageModules.ClinicDashboard />,
          },
          { path: SHOP_ROUTES_PATH.cart, element: <pageModules.Cart /> },
          { path: SHOP_ROUTES_PATH.search, element: <pageModules.Search /> },
          {
            path: SHOP_ROUTES_PATH.shoppingList,
            element: <pageModules.ShoppingList />,
          },
          {
            path: SHOP_ROUTES_PATH.productItem,
            element: <pageModules.ProductDetails />,
          },
          {
            path: SHOP_ROUTES_PATH.orderHistory,
            element: <pageModules.OrderHistory />,
          },
          {
            path: SHOP_ROUTES_PATH.orderHistoryItem,
            element: <pageModules.OrderHistory />,
          },
          { path: SHOP_ROUTES_PATH.vendors, element: <pageModules.Vendors /> },
          {
            path: SHOP_ROUTES_PATH.invoices,
            element: (
              <MoniteFeatureGuard>
                <pageModules.Invoices />
              </MoniteFeatureGuard>
            ),
          },
          {
            path: SHOP_ROUTES_PATH.settings,
            element: <SettingsLayout title="Clinic Settings" />,
            children: [
              {
                element: <pageModules.Settings />,
                index: true,
              },
            ],
          },
        ],
      },
      {
        path: '/',
        element: <ClinicLayout showCart={false} />,
        children: [
          {
            path: SHOP_ROUTES_PATH.checkout,
            element: <pageModules.Checkout />,
          },
        ],
      },
      {
        path: '/',
        element: <AccountLayout />,
        children: [
          {
            path: SHOP_ROUTES_PATH.clinicManagement,
            element: <pageModules.ClinicManagement />,
          },
          {
            path: SHOP_ROUTES_PATH.accountDashboard,
            element: <pageModules.Dashboard />,
          },
          {
            path: SHOP_ROUTES_PATH.accountDetails,
            element: <SettingsLayout title="Account Info" />,
            children: [
              { index: true, element: <pageModules.AccountDetails /> },
            ],
          },
        ],
      },
      {
        path: '*',
        element: <pageModules.Page404 />,
      },
    ],
  },
];

const router = createBrowserRouter(routes);

export default router;
