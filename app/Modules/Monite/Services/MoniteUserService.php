<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use App\Models\Clinic;
use App\Models\User;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Exceptions\MoniteApiException;
use App\Modules\Monite\Models\MoniteRoleMapping;
use App\Modules\Monite\Models\MoniteUserMapping;
use App\Modules\Monite\Traits\ChecksMoniteFeatureFlag;
use Illuminate\Support\Facades\Log;

class MoniteUserService
{
    use ChecksMoniteFeatureFlag;

    public function __construct(
        private readonly MoniteApiClientInterface $moniteClient
    ) {}

    /**
     * Create or update a Monite user for a clinic user
     */
    public function createOrUpdateUserForClinic(User $user, Clinic $clinic): ?string
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'createOrUpdateUserForClinic')) {
            return null;
        }

        $mapping = MoniteUserMapping::where('user_id', $user->id)
            ->where('clinic_id', $clinic->id)
            ->first();

        if ($mapping) {
            return $this->updateMoniteUser($mapping, $user, $clinic);
        }

        return $this->createMoniteUser($user, $clinic);
    }

    /**
     * Delete a Monite user mapping
     */
    public function deleteUserForClinic(User $user, Clinic $clinic): void
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'deleteUserForClinic')) {
            return;
        }

        $mapping = MoniteUserMapping::where('user_id', $user->id)
            ->where('clinic_id', $clinic->id)
            ->first();

        if (! $mapping) {
            return;
        }

        try {
            $response = $this->moniteClient
                ->withEntityId($mapping->entity_id)
                ->delete("/entity_users/{$mapping->monite_user_id}");

            if (! $response->successful() && $response->status() !== 404) {
                Log::error('Monite user deletion failed - API Response', [
                    'clinic_id' => $clinic->id,
                    'user_id' => $user->id,
                    'monite_user_id' => $mapping->monite_user_id,
                    'entity_id' => $mapping->entity_id,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                ]);

                throw MoniteApiException::fromResponse($response);
            }

            // Delete the mapping
            $mapping->delete();

            Log::info('Monite user deleted for clinic', [
                'clinic_id' => $clinic->id,
                'user_id' => $user->id,
                'monite_user_id' => $mapping->monite_user_id,
                'entity_id' => $mapping->entity_id,
            ]);

        } catch (MoniteApiException $e) {
            Log::error('Failed to delete Monite user for clinic', [
                'clinic_id' => $clinic->id,
                'user_id' => $user->id,
                'monite_user_id' => $mapping->monite_user_id,
                'entity_id' => $mapping->entity_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Get Monite user ID for a clinic user
     */
    public function getMoniteUserId(User $user, Clinic $clinic): ?string
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'getMoniteUserId')) {
            return null;
        }

        $mapping = MoniteUserMapping::where('user_id', $user->id)
            ->where('clinic_id', $clinic->id)
            ->first();

        return $mapping?->monite_user_id;
    }

    /**
     * Sync all clinic users with Monite
     */
    public function syncAllUsersForClinic(Clinic $clinic): void
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'syncAllUsersForClinic')) {
            return;
        }

        if (! $clinic->monite_entity_id) {
            throw new MoniteApiException('Clinic must have a Monite entity before syncing users');
        }

        // Get all users associated with this clinic's account
        $users = $clinic->account->users;

        foreach ($users as $user) {
            $this->createOrUpdateUserForClinic($user, $clinic);
        }
    }

    /**
     * Delete all Monite users for a clinic
     */
    public function deleteAllUsersForClinic(Clinic $clinic): void
    {
        if (! $this->ensureMoniteFeatureEnabled($clinic, 'deleteAllUsersForClinic')) {
            return;
        }

        $mappings = MoniteUserMapping::where('clinic_id', $clinic->id)->get();

        foreach ($mappings as $mapping) {
            try {
                $response = $this->moniteClient
                    ->withEntityId($mapping->entity_id)
                    ->delete("/entity_users/{$mapping->monite_user_id}");

                if (! $response->successful() && $response->status() !== 404) {
                    Log::error('Monite user deletion failed - API Response', [
                        'clinic_id' => $clinic->id,
                        'user_id' => $mapping->user_id,
                        'monite_user_id' => $mapping->monite_user_id,
                        'entity_id' => $mapping->entity_id,
                        'status_code' => $response->status(),
                        'response_body' => $response->body(),
                    ]);
                }

                $mapping->delete();

            } catch (MoniteApiException $e) {
                Log::error('Failed to delete Monite user for clinic', [
                    'clinic_id' => $clinic->id,
                    'user_id' => $mapping->user_id,
                    'monite_user_id' => $mapping->monite_user_id,
                    'entity_id' => $mapping->entity_id,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }

    /**
     * Create a new Monite user
     */
    private function createMoniteUser(User $user, Clinic $clinic): string
    {
        if (! $clinic->monite_entity_id) {
            throw new MoniteApiException('Clinic must have a Monite entity before creating users');
        }

        try {
            $userData = $this->prepareUserData($user, $clinic);

            Log::info('Creating Monite user - Request payload', [
                'clinic_id' => $clinic->id,
                'user_id' => $user->id,
                'entity_id' => $clinic->monite_entity_id,
                'payload' => $userData,
            ]);

            $response = $this->moniteClient
                ->withEntityId($clinic->monite_entity_id)
                ->post('/entity_users', $userData);

            if (! $response->successful()) {
                Log::error('Monite user creation failed - API Response', [
                    'clinic_id' => $clinic->id,
                    'user_id' => $user->id,
                    'entity_id' => $clinic->monite_entity_id,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                ]);

                throw MoniteApiException::fromResponse($response);
            }

            $responseData = $response->json();
            $moniteUserId = $responseData['id'];

            // Create the mapping
            MoniteUserMapping::create([
                'user_id' => $user->id,
                'monite_user_id' => $moniteUserId,
                'clinic_id' => $clinic->id,
                'entity_id' => $clinic->monite_entity_id,
            ]);

            Log::info('Monite user created for clinic', [
                'clinic_id' => $clinic->id,
                'user_id' => $user->id,
                'monite_user_id' => $moniteUserId,
                'entity_id' => $clinic->monite_entity_id,
            ]);

            return $moniteUserId;

        } catch (MoniteApiException $e) {
            Log::error('Failed to create Monite user for clinic', [
                'clinic_id' => $clinic->id,
                'user_id' => $user->id,
                'entity_id' => $clinic->monite_entity_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Update an existing Monite user
     */
    private function updateMoniteUser(MoniteUserMapping $mapping, User $user, Clinic $clinic): string
    {
        try {
            $userData = $this->prepareUserData($user, $clinic);

            Log::info('Updating Monite user - Request payload', [
                'clinic_id' => $clinic->id,
                'user_id' => $user->id,
                'monite_user_id' => $mapping->monite_user_id,
                'entity_id' => $mapping->entity_id,
                'payload' => $userData,
            ]);

            $response = $this->moniteClient
                ->withEntityId($mapping->entity_id)
                ->patch("/entity_users/{$mapping->monite_user_id}", $userData);

            if (! $response->successful()) {
                Log::error('Monite user update failed - API Response', [
                    'clinic_id' => $clinic->id,
                    'user_id' => $user->id,
                    'monite_user_id' => $mapping->monite_user_id,
                    'entity_id' => $mapping->entity_id,
                    'status_code' => $response->status(),
                    'response_body' => $response->body(),
                    'response_json' => $response->json(),
                ]);

                throw MoniteApiException::fromResponse($response);
            }

            Log::info('Monite user updated for clinic', [
                'clinic_id' => $clinic->id,
                'user_id' => $user->id,
                'monite_user_id' => $mapping->monite_user_id,
                'entity_id' => $mapping->entity_id,
            ]);

            return $mapping->monite_user_id;

        } catch (MoniteApiException $e) {
            Log::error('Failed to update Monite user for clinic', [
                'clinic_id' => $clinic->id,
                'user_id' => $user->id,
                'monite_user_id' => $mapping->monite_user_id,
                'entity_id' => $mapping->entity_id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Prepare user data for Monite API based on local user and clinic
     */
    private function prepareUserData(User $user, Clinic $clinic): array
    {
        // Check if user has any roles
        if ($user->roles->isEmpty()) {
            Log::warning("User {$user->id} ({$user->email}) has no roles assigned, skipping Monite user creation");
            throw new MoniteApiException("User {$user->id} ({$user->email}) has no roles assigned");
        }

        // Get the user's first role
        $userRole = $user->roles->first();

        // Get the role mapping for this clinic
        $roleMapping = MoniteRoleMapping::where('clinic_id', $clinic->id)
            ->where('role_id', $userRole->id)
            ->first();

        if (! $roleMapping) {
            $availableRoles = MoniteRoleMapping::where('clinic_id', $clinic->id)
                ->with('role')
                ->get()
                ->pluck('role.name')
                ->toArray();

            throw new MoniteApiException(
                "User {$user->id} ({$user->email}) has role '{$userRole->name}' but no Monite mapping exists for clinic {$clinic->id}. ".
                'Available roles: '.implode(', ', $availableRoles)
            );
        }

        return [
            'login' => $user->email,
            'first_name' => $user->name,
            'role_id' => $roleMapping->monite_role_id,
            'email' => $user->email,
            'phone' => $user->phone ?? '',
        ];
    }
}
