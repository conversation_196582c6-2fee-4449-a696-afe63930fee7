<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use App\Models\Clinic;
use App\Modules\Monite\Models\MoniteInvoice;
use Illuminate\Support\Facades\Log;

final class MonitePayableProcessor
{
    /**
     * Process a payable and create or update the corresponding invoice
     */
    public function process(array $payableData, Clinic $clinic): array
    {
        $payableId = $payableData['id'];

        Log::info('Processing payable', [
            'payable_id' => $payableId,
            'clinic_id' => $clinic->id,
        ]);

        // Use updateOrCreate to handle both create and update scenarios
        $invoice = MoniteInvoice::updateOrCreate(
            [
                'monite_payable_id' => $payableId,
                'clinic_id' => $clinic->id,
            ],
            [
                'vendor_id' => null,
                'external_order_id' => null,
                'invoice_number' => $payableData['document_id'],
                'status' => $payableData['status'],
                'amount' => $payableData['total_amount'] / 100, // Convert from cents to dollars
                'currency' => $payableData['currency'],
                'due_date' => isset($payableData['due_date']) ? date('Y-m-d', strtotime($payableData['due_date'])) : null,
                'vendor_name' => $payableData['counterpart_raw_data']['name'] ?? null,
            ]
        );

        $wasRecentlyCreated = $invoice->wasRecentlyCreated;

        Log::info($wasRecentlyCreated ? 'Created new invoice from payable' : 'Updated existing invoice', [
            'invoice_id' => $invoice->id,
            'payable_id' => $payableData['id'],
            'clinic_id' => $clinic->id,
        ]);

        return [
            'matched' => ! $wasRecentlyCreated,
            'invoice_id' => $invoice->id,
            'external_order_id' => $invoice->external_order_id,
            'action' => $wasRecentlyCreated ? 'created' : 'updated',
        ];
    }
}
