<?php

declare(strict_types=1);

use App\Models\Cart;
use App\Models\CartItem;
use App\Models\Clinic;
use App\Models\Product;
use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Models\Promotion;
use App\Modules\Promotion\Services\CartPromotionEligibilityService;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

it('only applies promotions to cart items from the correct vendor', function () {
    // Create vendors
    $covetrusVendor = Vendor::factory()->create(['name' => 'Covetrus']);
    $pattersonVendor = Vendor::factory()->create(['name' => 'Patterson']);

    // Create GPO account
    $gpoAccount = GpoAccount::factory()->create();

    // Create clinic
    $clinic = Clinic::factory()->create();
    $clinic->account->update(['gpo_account_id' => $gpoAccount->id]);

    // Connect clinic to both vendors
    App\Modules\Integration\Models\IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $covetrusVendor->id,
        'status' => 'connected',
        'credentials' => ['test' => 'data'],
    ]);
    App\Modules\Integration\Models\IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $pattersonVendor->id,
        'status' => 'connected',
        'credentials' => ['test' => 'data'],
    ]);

    // Create product
    $product = Product::factory()->create(['sku' => '084416']);

    // Create product offers for both vendors
    $covetrusProductOffer = ProductOffer::factory()->create([
        'product_id' => $product->id,
        'vendor_id' => $covetrusVendor->id,
        'vendor_sku' => '084416',
    ]);

    $pattersonProductOffer = ProductOffer::factory()->create([
        'product_id' => $product->id,
        'vendor_id' => $pattersonVendor->id,
        'vendor_sku' => '084416',
    ]);

    // Create promotion ONLY for Covetrus
    $promotion = Promotion::factory()->create([
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $gpoAccount->id,
        'vendor_id' => $covetrusVendor->id,
        'name' => 'Buy 1 Get 1 FREE – Covetrus Only',
        'type' => PromotionType::BuyXGetY,
        'status' => PromotionStatus::Active,
        'started_at' => now()->subDay(),
        'ended_at' => now()->addDay(),
    ]);

    // Link promotion only to Covetrus product offer
    $promotion->productOffers()->attach($covetrusProductOffer->id);

    // Create a rule for the promotion (required for eligibility)
    $rule = App\Modules\Promotion\Models\PromotionRule::create([
        'promotion_id' => $promotion->id,
        'priority' => 1,
    ]);

    // Create cart with items from both vendors
    $cart = Cart::factory()->create(['clinic_id' => $clinic->id]);

    $covetrusCartItem = CartItem::factory()->create([
        'cart_id' => $cart->id,
        'product_offer_id' => $covetrusProductOffer->id,
        'quantity' => 2,
    ]);

    $pattersonCartItem = CartItem::factory()->create([
        'cart_id' => $cart->id,
        'product_offer_id' => $pattersonProductOffer->id,
        'quantity' => 2,
    ]);

    // Load relationships
    $cart->load('items.productOffer.vendor');

    // Test cart promotion eligibility
    $promotionService = app(CartPromotionEligibilityService::class);
    $result = $promotionService->checkCartEligibility($cart);

    // Should only have one eligible promotion
    expect($result['eligiblePromotions'])->toHaveCount(1);
    expect($result['eligiblePromotions'][0]['promotion']->name)->toBe('Buy 1 Get 1 FREE – Covetrus Only');

    // Should only apply to Covetrus cart item, not Patterson
    expect($result['itemsWithPromotions'])->toHaveKey($covetrusCartItem->id);
    expect($result['itemsWithPromotions'])->not->toHaveKey($pattersonCartItem->id);

    // Verify the promotion is only applied to the Covetrus item
    expect($result['itemsWithPromotions'][$covetrusCartItem->id])->toHaveCount(1);
    expect($result['itemsWithPromotions'][$covetrusCartItem->id][0]['promotionId'])->toBe($promotion->id);
});

it('does not apply promotions when clinic is not connected to the promotion vendor', function () {
    // Create vendors
    $covetrusVendor = Vendor::factory()->create(['name' => 'Covetrus']);
    $pattersonVendor = Vendor::factory()->create(['name' => 'Patterson']);

    // Create GPO account
    $gpoAccount = GpoAccount::factory()->create();

    // Create clinic
    $clinic = Clinic::factory()->create();
    $clinic->account->update(['gpo_account_id' => $gpoAccount->id]);

    // Connect clinic ONLY to Patterson vendor (not Covetrus)
    App\Modules\Integration\Models\IntegrationConnection::create([
        'clinic_id' => $clinic->id,
        'vendor_id' => $pattersonVendor->id,
        'status' => 'connected',
        'credentials' => ['test' => 'data'],
    ]);

    // Create product
    $product = Product::factory()->create(['sku' => '084416']);

    // Create product offers for both vendors
    $covetrusProductOffer = ProductOffer::factory()->create([
        'product_id' => $product->id,
        'vendor_id' => $covetrusVendor->id,
        'vendor_sku' => '084416',
    ]);

    $pattersonProductOffer = ProductOffer::factory()->create([
        'product_id' => $product->id,
        'vendor_id' => $pattersonVendor->id,
        'vendor_sku' => '084416',
    ]);

    // Create promotion for Covetrus (but clinic is not connected to Covetrus)
    $promotion = Promotion::factory()->create([
        'promotionable_type' => GpoAccount::class,
        'promotionable_id' => $gpoAccount->id,
        'vendor_id' => $covetrusVendor->id,
        'name' => 'Buy 1 Get 1 FREE – Covetrus Only',
        'type' => PromotionType::BuyXGetY,
        'status' => PromotionStatus::Active,
        'started_at' => now()->subDay(),
        'ended_at' => now()->addDay(),
    ]);

    // Link promotion to Covetrus product offer
    $promotion->productOffers()->attach($covetrusProductOffer->id);

    // Create a rule for the promotion (required for eligibility)
    $rule = App\Modules\Promotion\Models\PromotionRule::create([
        'promotion_id' => $promotion->id,
        'priority' => 1,
    ]);

    // Create cart with items from both vendors
    $cart = Cart::factory()->create(['clinic_id' => $clinic->id]);

    $covetrusCartItem = CartItem::factory()->create([
        'cart_id' => $cart->id,
        'product_offer_id' => $covetrusProductOffer->id,
        'quantity' => 2,
    ]);

    $pattersonCartItem = CartItem::factory()->create([
        'cart_id' => $cart->id,
        'product_offer_id' => $pattersonProductOffer->id,
        'quantity' => 2,
    ]);

    // Load relationships
    $cart->load('items.productOffer.vendor');

    // Test cart promotion eligibility
    $promotionService = app(CartPromotionEligibilityService::class);
    $result = $promotionService->checkCartEligibility($cart);

    // Should have no eligible promotions since clinic is not connected to Covetrus
    expect($result['eligiblePromotions'])->toBeEmpty();
    expect($result['itemsWithPromotions'])->toBeEmpty();
});
