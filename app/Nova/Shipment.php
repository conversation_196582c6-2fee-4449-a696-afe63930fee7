<?php

declare(strict_types=1);

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\BelongsToMany;
use <PERSON><PERSON>\Nova\Fields\DateTime;
use <PERSON><PERSON>\Nova\Fields\Field;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\URL;
use <PERSON><PERSON>\Nova\Http\Requests\NovaRequest;

final class Shipment extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Shipment>
     */
    public static $model = \App\Models\Shipment::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'tracking_number';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'tracking_number',
        'carrier',
    ];

    public function subtitle(): string
    {
        return $this->carrier;
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array<int, Field>
     */
    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),
            Text::make('Tracking Number')->sortable(),
            Text::make('Carrier')->sortable(),
            URL::make('Tracking Link')->nullable(),
            DateTime::make('ETA Date From')->nullable(),
            DateTime::make('ETA Date To')->nullable(),
            DateTime::make('Date Delivered')->nullable(),
            BelongsToMany::make('Items', 'items', OrderItem::class),
        ];
    }
}
