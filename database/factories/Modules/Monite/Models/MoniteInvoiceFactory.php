<?php

declare(strict_types=1);

namespace Database\Factories\Modules\Monite\Models;

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\Monite\Models\MoniteInvoice;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Modules\Monite\Models\MoniteInvoice>
 */
final class MoniteInvoiceFactory extends Factory
{
    protected $model = MoniteInvoice::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'clinic_id' => Clinic::factory(),
            'vendor_id' => Vendor::factory(),
            'external_order_id' => null,
            'invoice_number' => 'INV-'.$this->faker->unique()->numerify('#####'),
            'status' => 'pending',
            'amount' => $this->faker->randomFloat(2, 10, 1000),
            'currency' => 'USD',
            'due_date' => $this->faker->dateTimeBetween('now', '+30 days'),
            'vendor_name' => $this->faker->company,
            'monite_payable_id' => null,
        ];
    }
}
