<?php

declare(strict_types=1);

namespace Database\Factories\Modules\Monite\Models;

use App\Enums\MoniteWebhookEventStatus;
use App\Modules\Monite\Models\MoniteWebhookEvent;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Modules\Monite\Models\MoniteWebhookEvent>
 */
final class MoniteWebhookEventFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var class-string<MoniteWebhookEvent>
     */
    protected $model = MoniteWebhookEvent::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'event_id' => $this->faker->uuid(),
            'event_type' => $this->faker->randomElement(['payable.created', 'payable.updated', 'counterpart.created']),
            'entity_id' => $this->faker->uuid(),
            'object_type' => $this->faker->randomElement(['payable', 'counterpart']),
            'object_id' => $this->faker->uuid(),
            'payload' => [
                'id' => $this->faker->uuid(),
                'action' => $this->faker->randomElement(['payable.created', 'payable.updated', 'counterpart.created']),
                'entity_id' => $this->faker->uuid(),
                'object_type' => $this->faker->randomElement(['payable', 'counterpart']),
                'object' => [
                    'id' => $this->faker->uuid(),
                ],
            ],
            'status' => MoniteWebhookEventStatus::PENDING,
        ];
    }

    /**
     * Indicate that the event is processed.
     */
    public function processed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => MoniteWebhookEventStatus::PROCESSED,
            'processed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the event failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => MoniteWebhookEventStatus::FAILED,
            'error_message' => $this->faker->sentence(),
            'processed_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }
}
