<?php

declare(strict_types=1);

namespace App\Nova;

use App\Enums\OrderItemStatus;
use App\Enums\PaymentMethod;
use App\Modules\Account\Nova\User;
use App\Nova\Actions\ExportOrderCsv;
use App\Traits\Statusable;
use <PERSON><PERSON>\Nova\Fields\Badge;
use <PERSON>vel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\Currency;
use Laravel\Nova\Fields\Date;
use Laravel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\MorphOne;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class Order extends Resource
{
    use Statusable;

    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\Order>
     */
    public static $model = \App\Models\Order::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'order_number';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'order_number',
        'clinic.name',
        'user.name',
        'items.productOffer.name',
        'suborders.vendor.name',
    ];

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            Date::make('Order Date', 'created_at')
                ->sortable(),

            Text::make('Order Number', 'order_number')
                ->sortable(),

            Badge::make('Status', 'status')
                ->map($this->generateOptionMapping())
                ->labels(OrderItemStatus::options())
                ->sortable(),

            BelongsTo::make('Clinic', 'clinic', Clinic::class)->sortable(),

            BelongsTo::make('User', 'user', User::class)->sortable(),

            Select::make('Payment Method', 'payment_method')
                ->options(PaymentMethod::options())
                ->displayUsingLabels()
                ->sortable(),

            Currency::make('Total Price', 'total_price')
                ->currency('USD')
                ->asMinorUnits(),

            MorphOne::make('Shipping Address', 'shippingAddress', Address::class),

            MorphOne::make('Billing Address', 'billingAddress', Address::class),

            HasMany::make('Sub Orders', 'suborders', SubOrder::class),

            HasMany::make('Items', 'items', OrderItem::class),

            HasMany::make('Promotions', 'promotions', OrderPromotion::class),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            resolve(ExportOrderCsv::class)
                ->sole()
                ->exceptOnIndex()
                ->showInline()
                ->withoutConfirmation(),
        ];
    }
}
