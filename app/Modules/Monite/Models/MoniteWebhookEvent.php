<?php

declare(strict_types=1);

namespace App\Modules\Monite\Models;

use App\Enums\MoniteWebhookEventStatus;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MoniteWebhookEvent extends Model
{
    use HasFactory;
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'event_id',
        'event_type',
        'entity_id',
        'object_type',
        'object_id',
        'payload',
        'status',
        'error_message',
        'processed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'payload' => 'array',
        'processed_at' => 'datetime',
        'status' => MoniteWebhookEventStatus::class,
    ];

    /**
     * Mark the event as processing
     */
    public function markAsProcessing(): void
    {
        $this->update([
            'status' => MoniteWebhookEventStatus::PROCESSING,
        ]);
    }

    /**
     * Mark the event as processed
     */
    public function markAsProcessed(): void
    {
        $this->update([
            'status' => MoniteWebhookEventStatus::PROCESSED,
            'processed_at' => now(),
        ]);
    }

    /**
     * Mark the event as failed with error message
     */
    public function markAsFailed(string $errorMessage): void
    {
        $this->update([
            'status' => MoniteWebhookEventStatus::FAILED,
            'error_message' => $errorMessage,
            'processed_at' => now(),
        ]);
    }

    /**
     * Mark the event as ignored (e.g., unsupported event type)
     */
    public function markAsIgnored(?string $reason): void
    {
        $this->update([
            'status' => MoniteWebhookEventStatus::IGNORED,
            'error_message' => $reason,
            'processed_at' => now(),
        ]);
    }

    /**
     * Scope query to pending events
     */
    public function scopePending($query)
    {
        return $query->where('status', MoniteWebhookEventStatus::PENDING);
    }

    /**
     * Scope query to failed events
     */
    public function scopeFailed($query)
    {
        return $query->where('status', MoniteWebhookEventStatus::FAILED);
    }

    /**
     * Scope query to payable events
     */
    public function scopePayableEvents($query)
    {
        return $query->whereIn('event_type', ['payable.created', 'payable.updated']);
    }

    /**
     * Scope query to specific event types
     */
    public function scopeOfType($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }
}
