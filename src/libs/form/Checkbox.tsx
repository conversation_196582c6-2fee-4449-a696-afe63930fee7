import { forwardRef, InputHTMLAttributes, useId } from 'react';
import { mergeClasses } from '@/utils';

type InputProps = {
  id?: string;
  label?: string;
  error?: string;
  align?: 'left' | 'center' | 'right';
  className?: string;
} & InputHTMLAttributes<HTMLInputElement>;

export const Checkbox = forwardRef<HTMLInputElement, InputProps>(
  ({ id, label, error, align = 'left', onChange, ...rest }, ref) => {
    const generatedId = useId();
    const inputId = id || generatedId;

    const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      if (onChange) {
        onChange(e);
      }
    };

    const alignmentClass = {
      left: 'justify-start',
      center: 'justify-center',
      right: 'justify-end',
    }[align];

    return (
      <>
        <div
          className={mergeClasses(
            'relative flex items-center gap-2',
            alignmentClass,
          )}
        >
          <input
            id={inputId}
            ref={ref}
            onChange={handleChange}
            aria-invalid={!!error}
            type="checkbox"
            style={{ textAlign: align }}
            {...rest}
          />
          {label && (
            <label htmlFor={inputId} className="text-sm">
              {label}
            </label>
          )}
        </div>
        {error && (
          <p className="mt-1.5 max-w-full text-xs text-red-700">{error}</p>
        )}
      </>
    );
  },
);

Checkbox.displayName = 'Checkbox';
