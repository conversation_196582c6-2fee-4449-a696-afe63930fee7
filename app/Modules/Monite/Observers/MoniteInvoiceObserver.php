<?php

declare(strict_types=1);

namespace App\Modules\Monite\Observers;

use App\Modules\Monite\Actions\MatchExternalOrderWithInvoiceAction;
use App\Modules\Monite\Models\MoniteInvoice;

final class MoniteInvoiceObserver
{
    public function __construct(
        private readonly MatchExternalOrderWithInvoiceAction $matchExternalOrderWithInvoiceAction
    ) {}

    /**
     * Handle the MoniteInvoice "created" event.
     */
    public function created(MoniteInvoice $moniteInvoice): void
    {
        // Check if external_order_id is null and invoice_number exists
        if (is_null($moniteInvoice->external_order_id) && $moniteInvoice->invoice_number) {
            $this->matchExternalOrderWithInvoiceAction->execute($moniteInvoice);
        }
    }

    /**
     * Handle the MoniteInvoice "updated" event.
     */
    public function updated(MoniteInvoice $moniteInvoice): void
    {
        // Check if external_order_id was set to null or invoice_number was updated
        if (($moniteInvoice->wasChanged('external_order_id') && is_null($moniteInvoice->external_order_id)) ||
            ($moniteInvoice->wasChanged('invoice_number') && $moniteInvoice->invoice_number)) {
            $this->matchExternalOrderWithInvoiceAction->execute($moniteInvoice);
        }
    }

    /**
     * Handle the MoniteInvoice "deleted" event.
     */
    public function deleted(MoniteInvoice $moniteInvoice): void
    {
        //
    }

    /**
     * Handle the MoniteInvoice "restored" event.
     */
    public function restored(MoniteInvoice $moniteInvoice): void
    {
        //
    }

    /**
     * Handle the MoniteInvoice "force deleted" event.
     */
    public function forceDeleted(MoniteInvoice $moniteInvoice): void
    {
        //
    }
}
