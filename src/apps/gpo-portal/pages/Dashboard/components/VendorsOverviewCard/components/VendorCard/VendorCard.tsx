import { GpoVendorData } from '../../types';
import { getPriceString, formatCompactNumber, mergeClasses } from '@/utils';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';
import { Badge } from '@/libs/ui/Badge/Badge';
import { ProgressBar } from '@/libs/ui/ProgressBar/ProgressBar';

interface VendorCardProps {
  vendorData: GpoVendorData;
  totalSpend: number;
}

export const VendorCard = ({ vendorData, totalSpend }: VendorCardProps) => {
  const formatPercentage = (value: number) => {
    return `${value}%`;
  };

  const calculateProgressPercentage = () => {
    const vendorTotalSpend = +vendorData.totalSpend;
    const goalAmount = +vendorData.goalAmount;

    return Math.min(vendorTotalSpend / goalAmount, 1);
  };

  const hasGoalSet = vendorData.goalAmount > 0;
  const isGoalReached = vendorData.amountUntilGoal <= 0;
  const progressPercentage = calculateProgressPercentage();

  return (
    <div className="rounded-xl border border-[#F5F5F5] bg-white p-5">
      <div className="mb-4 flex items-center justify-between">
        <div className="flex w-[268px] items-center gap-3">
          <div className="flex h-12 flex-col items-center justify-center rounded-lg text-center">
            <img
              src={vendorData.imageUrl}
              alt={vendorData.name}
              className="h-full w-auto"
            />
          </div>
          <div className="flex flex-col gap-[0.3rem]">
            <h4 className="text-[1rem] font-medium text-[#344054]">
              {vendorData.name}
            </h4>
            <p className="text-[10px] font-bold text-[#666] capitalize">
              {vendorData.type}
            </p>
          </div>
        </div>

        <div className="divider-v h-8" />

        <div className="flex flex-1 items-center justify-between">
          <div className="flex min-w-[190px] flex-col px-8">
            <p className="mb-1 text-xs text-[#666]">Market Share %</p>
            <div className="flex items-center gap-2">
              <p className="text-sm font-semibold text-[#344054]">
                {formatPercentage(vendorData.marketSharePercentage)}
                <span className="ml-1 text-xs text-[#344054] opacity-60">
                  ({formatCompactNumber(vendorData.totalSpend)} from{' '}
                  {formatCompactNumber(totalSpend)} )
                </span>
              </p>
            </div>
          </div>
          <div className="divider-v h-8" />
          <div className="flex min-w-[190px] flex-col px-8">
            <p className="mb-1 text-xs text-[#666]">Total Spend</p>
            <div className="flex items-center gap-2">
              <p className="text-sm font-semibold text-[#344054]">
                {getPriceString(vendorData.totalSpend)}
              </p>
            </div>
          </div>
          <div className="divider-v h-8" />
          <div className="flex min-w-[190px] flex-col px-8">
            <p className="mb-1 text-xs text-[#666]">Growth Target %</p>
            <div className="flex items-center gap-2">
              <p className="text-sm font-semibold text-[#344054]">
                {formatPercentage(vendorData.growthTargetPercentage)}
                <span className="ml-1 text-xs text-[#344054] opacity-60">
                  ({formatCompactNumber(vendorData.totalSpend)} from{' '}
                  {formatCompactNumber(vendorData.goalAmount)} )
                </span>
              </p>
            </div>
          </div>
        </div>

        <div className="flex w-[80px] items-center justify-end gap-2">
          {/* TODO: Add download button */}
          {/*
          <Button
            variant="white"
            className="max-w-[60px]"
            aria-label="Download"
            onClick={() => {
              console.log('WIP...');
            }}
          >
            <Icon name="download" aria-hidden={true} />
          </Button> */}
          <Button
            variant="unstyled"
            className="max-w-[60px]"
            aria-label="More options"
            onClick={() => {
              console.log('WIP...');
            }}
          >
            <Icon name="moreOptions" aria-hidden={true} />
          </Button>
        </div>
      </div>

      <div className="divider-h w-full" />

      <div className="mt-4 flex items-center rounded-full bg-[#F2F2F2] py-[14px]">
        <div className="flex w-[268px] items-center justify-between pl-4">
          <p className="text-xs font-normal text-[#98A2B3]">
            {isGoalReached ? (
              <span className="text-sm font-bold text-[#333]">
                Goal Reached 🎉
              </span>
            ) : hasGoalSet ? (
              <>
                <span className="text-sm font-bold text-[#333]">
                  {getPriceString(vendorData.amountUntilGoal)}{' '}
                </span>
                until your goal
              </>
            ) : (
              <span className="text-sm font-bold text-[#333]">No goal set</span>
            )}
          </p>
        </div>
        <div className="divider-v h-8" />
        <div className="flex flex-1 items-center justify-between pr-4">
          <Badge className="h-[20px] w-[68px] bg-[#518ef8] p-0 text-xs font-bold text-white">
            Start
          </Badge>
          <div className="flex-1 px-2 pt-4">
            <ProgressBar
              showLegend={false}
              values={[
                {
                  value: progressPercentage * 100,
                  color: '#518ef8',
                },
                {
                  value: 100 - progressPercentage * 100,
                  color: '#EEE9FC',
                },
              ]}
            />
          </div>
          <Badge
            className={mergeClasses(
              'h-[20px] w-[68px] p-0 text-xs font-bold',
              isGoalReached
                ? 'bg-[#518ef8] text-white'
                : 'bg-[rgba(82,82,82,0.4)] text-white',
            )}
          >
            Goal
          </Badge>
        </div>
      </div>
    </div>
  );
};
