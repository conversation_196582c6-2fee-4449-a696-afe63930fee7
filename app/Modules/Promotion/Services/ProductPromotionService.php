<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Services;

use App\Models\Clinic;
use App\Models\Product;
use App\Models\ProductOffer as ProductOfferModel;
use App\Modules\Gpo\Models\GpoAccount;
use App\Modules\Promotion\Data\PromotionData;
use App\Modules\Promotion\Models\Promotion;
use Illuminate\Support\Collection;

final class ProductPromotionService
{
    public function getActivePromotionsForProductOffer(ProductOfferModel $productOffer, Clinic $clinic): Collection
    {
        $query = Promotion::query()
            ->active()
            ->with([
                'vendor',
                'productOffers.product',
                'productOffers.vendor',
                'rules.conditions',
                'rules.actions',
            ]);

        // Filter by clinic's GPO account (required)
        $gpoId = $clinic->account?->gpo_account_id;

        if ($gpoId) {
            $query->whereHasMorph('promotionable', [GpoAccount::class], function ($query) use ($gpoId) {
                $query->where('id', $gpoId);
            });
        }

        // Filter promotions that apply to this product AND ensure vendor matches
        $query->whereHas('productOffers', function ($query) use ($productOffer) {
            $query->where('id', $productOffer->id)
                ->whereColumn('product_offers.vendor_id', 'promotions.vendor_id');
        });

        $promotions = $query->get();

        return $promotions->map(fn (Promotion $promotion) => PromotionData::from($promotion));
    }

    public function getActivePromotionsForProduct(Product $product, Clinic $clinic): Collection
    {
        $query = Promotion::query()
            ->active()
            ->with([
                'vendor',
                'productOffers.product',
                'productOffers.vendor',
                'rules.conditions',
                'rules.actions',
            ]);

        // Filter by clinic's GPO account (required)
        $gpoId = $clinic->account?->gpo_account_id;

        if ($gpoId) {
            $query->whereHasMorph('promotionable', [GpoAccount::class], function ($query) use ($gpoId) {
                $query->where('id', $gpoId);
            });
        }

        // Filter by connected vendors (DRY principle) - use already loaded relationship
        $connectedVendorIds = $clinic->connectedVendors->pluck('id')->toArray();
        $query->whereIn('vendor_id', $connectedVendorIds);

        // Filter promotions that apply to this product AND ensure vendor matches
        $query->whereHas('productOffers', function ($query) use ($product) {
            $query->where('product_id', $product->id)
                ->whereColumn('product_offers.vendor_id', 'promotions.vendor_id');
        });

        $promotions = $query->get();

        return $promotions->map(fn (Promotion $promotion) => PromotionData::from($promotion));
    }
}
