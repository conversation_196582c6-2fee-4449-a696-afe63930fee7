import { Text, Title } from '@mantine/core';
import { Menu, MenuItem } from '@/libs/ui/Menu/Menu';
import { Button } from '@/libs/ui/Button/Button';
import { CollapsiblePanel } from '@/libs/ui/CollapsiblePanel/CollapsiblePanel';
import { Badge } from '@/libs/ui/Badge/Badge';
import { EstimateRebateVendorPanel } from '../EstimateRebateVendorPanel/EstimateRebateVendorPanel';
import { EstimatedRebatesPanelLoader } from './EstimatedRebatesPanelLoader';
import { getPriceString } from '@/utils';
import { RebateType } from '@/types/common';
import { HelpTooltip } from '@/libs/ui/HelpTooltip/HelpTooltip';
import { useCallback } from 'react';
import { fetchApi } from '@/libs/utils/api';
import { useTimePeriod } from '@/libs/utils/hooks/useTimePeriod/useTimePeriod';
import dayjs from 'dayjs';
import { DEFAULT_DISPLAY_DATE_FORMAT } from '@/constants';
import { useQuery } from '@tanstack/react-query';
import { queryKeys } from '@/libs/query/queryClient';
import { Gradient } from '@/libs/ui/Gradient/Gradient';

export const EstimatedRebatesPanel = () => {
  const {
    options: rebatePeriodsOptions,
    period: selectedPeriod,
    setPeriod,
    endDate,
    startDate,
  } = useTimePeriod({
    defaultPeriod: 'entire year',
    availableOptions: ['entire year', 'Q1', 'Q2', 'Q3', 'Q4'],
  });

  const loadRebatesFunc = useCallback(async () => {
    const response = await fetchApi<RebateType[]>('/rebate-estimates');
    return response;
  }, []);

  const { data: rebates, isLoading } = useQuery({
    queryKey: queryKeys.rebates.estimates(),
    queryFn: loadRebatesFunc,
  });

  if (isLoading) {
    return <EstimatedRebatesPanelLoader />;
  }

  if (!rebates || rebates.length === 0) {
    return null;
  }

  const rebatesOnThePeriod = rebates.filter(({ promotion }) => {
    const promoStart = dayjs(promotion.startedAt);
    const promoEnd = dayjs(promotion.endedAt);

    const endsAfterOrOnStart =
      promoEnd.isAfter(startDate) || promoEnd.isSame(startDate, 'day');

    const startsBeforeOrOnEnd =
      promoStart.isBefore(endDate) || promoStart.isSame(endDate, 'day');

    return endsAfterOrOnStart && startsBeforeOrOnEnd;
  });

  const rebateData = rebatesOnThePeriod.reduce<{
    estimatedRebateAmount: number;
    vendorRebates: Record<string, RebateType[]>;
  }>(
    (acc, rebate) => ({
      estimatedRebateAmount:
        +rebate.estimatedRebateAmount + acc.estimatedRebateAmount,
      vendorRebates: {
        ...acc.vendorRebates,
        [rebate.promotion.vendor.id]: [
          ...(acc.vendorRebates[rebate.promotion.vendor.id] ?? []),
          rebate,
        ],
      },
    }),
    {
      estimatedRebateAmount: 0,
      vendorRebates: {},
    },
  );

  return (
    <CollapsiblePanel
      header={
        <div className="flex w-full items-center justify-between py-4 pr-16 pl-6">
          <div className="flex flex-1 items-center">
            <Title order={4} fw="500" mr="md">
              Rebate Programs
            </Title>
            <Gradient>
              <Badge>
                <div className="flex items-center gap-4">
                  <Text size="1rem" fw="500" tt="none">
                    Estimated Gross Earnings
                  </Text>
                  <Text size="1.5rem" fw="500" lh="3.3rem">
                    {getPriceString(rebateData.estimatedRebateAmount)}
                  </Text>
                </div>
              </Badge>
            </Gradient>
            <div className="ml-4">
              <HelpTooltip message="Projected rebate earnings on eligible Highfive purchases. For reference only — contact your GPO for final rebate amounts." />
            </div>
          </div>
          <Menu
            trigger={
              <div className="flex min-w-48 items-center">
                <Text miw="4.5rem">Filter by</Text>
                <Button variant="white">
                  <Text tt="capitalize" size="0.875rem" fw="500">
                    {selectedPeriod}
                  </Text>
                </Button>
              </div>
            }
            side="bottom"
            align="start"
          >
            {rebatePeriodsOptions.map((option) =>
              option !== selectedPeriod ? (
                <MenuItem key={option} onClick={() => setPeriod(option)}>
                  <Text tt="capitalize" size="0.875rem" miw="100px">
                    {option}
                  </Text>
                </MenuItem>
              ) : null,
            )}
          </Menu>
        </div>
      }
      content={
        <div className="grid p-6">
          <Text c="#666" size="0.75rem" mb="0.75rem">
            Results filtered by:{' '}
            <Text c="#333" fw="700" tt="capitalize" span>
              {selectedPeriod} ({startDate.format(DEFAULT_DISPLAY_DATE_FORMAT)}{' '}
              - {endDate.format(DEFAULT_DISPLAY_DATE_FORMAT)})
            </Text>
          </Text>
          <div className="grid gap-4">
            {Object.keys(rebateData.vendorRebates).map((vendorId) => (
              <EstimateRebateVendorPanel
                key={vendorId}
                rebates={rebateData.vendorRebates[vendorId]}
              />
            ))}
          </div>
        </div>
      }
      startOpen
    />
  );
};
