import { useState } from 'react';
import * as Select from '@radix-ui/react-select';

const PREDEFINED_COLORS = [
  { name: 'Red', value: '#EF4444', bgClass: 'bg-red-500' },
  { name: 'Blue', value: '#3B82F6', bgClass: 'bg-blue-500' },
  { name: 'Green', value: '#10B981', bgClass: 'bg-green-500' },
  { name: 'Yellow', value: '#F59E0B', bgClass: 'bg-yellow-500' },
  { name: 'Purple', value: '#8B5CF6', bgClass: 'bg-purple-500' },
  { name: 'Pink', value: '#EC4899', bgClass: 'bg-pink-500' },
  { name: 'Orange', value: '#F97316', bgClass: 'bg-orange-500' },
  { name: 'Gray', value: '#6B7280', bgClass: 'bg-gray-500' },
];

export interface ColorPickerProps {
  value?: string;
  onChange?: (color: string) => void;
  label?: string;
  className?: string;
}

export const ColorPicker = ({
  value = PREDEFINED_COLORS[0].value,
  onChange,
  label,
  className = '',
}: ColorPickerProps) => {
  const [selectedColor, setSelectedColor] = useState(value);

  const handleColorChange = (colorValue: string) => {
    setSelectedColor(colorValue);
    onChange?.(colorValue);
  };

  const currentColor =
    PREDEFINED_COLORS.find((color) => color.value === selectedColor) ||
    PREDEFINED_COLORS[0];

  return (
    <div className={`flex flex-col ${className}`}>
      {label && (
        <label className="mb-2 text-sm font-medium text-gray-700">
          {label}
        </label>
      )}

      <Select.Root value={selectedColor} onValueChange={handleColorChange}>
        <Select.Trigger className="inline-flex min-w-[120px] items-center justify-between rounded-md border border-gray-300 bg-white px-3 py-2 text-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:outline-none">
          <div className="flex items-center gap-2">
            <div
              className={`h-4 w-4 rounded-full ${currentColor.bgClass}`}
              style={{ backgroundColor: currentColor.value }}
            />
            <Select.Value />
          </div>
          <Select.Icon>
            <svg
              className="h-4 w-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </Select.Icon>
        </Select.Trigger>

        <Select.Portal>
          <Select.Content className="z-[9999] overflow-hidden rounded-md border border-gray-200 bg-white shadow-lg">
            <Select.Viewport className="p-1">
              {PREDEFINED_COLORS.map((color) => (
                <Select.Item
                  key={color.value}
                  value={color.value}
                  className="relative flex cursor-pointer items-center gap-2 rounded px-3 py-2 text-sm hover:bg-gray-100 focus:bg-gray-100 focus:outline-none data-[highlighted]:bg-gray-100"
                >
                  <div
                    className={`h-4 w-4 rounded-full ${color.bgClass}`}
                    style={{ backgroundColor: color.value }}
                  />
                  <Select.ItemText>{color.name}</Select.ItemText>
                  <Select.ItemIndicator className="absolute right-2">
                    <svg
                      className="h-4 w-4"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </Select.ItemIndicator>
                </Select.Item>
              ))}
            </Select.Viewport>
          </Select.Content>
        </Select.Portal>
      </Select.Root>
    </div>
  );
};
