<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Http\Resources\Products\Attribute;
use App\Models\ClinicProductFavorite;
use App\Models\ProductOffer as ProductOfferModel;
use App\Services\Sorting\ProductOfferSorter;
use Brick\Money\Money;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

final class ProductAlternativeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $product = $this->alternative;

        $clinicIds = $product->productOffers->flatMap(function (ProductOfferModel $productOffer) {
            return $productOffer->clinics->pluck('id');
        })->unique()->values()->toArray();

        return [
            'id' => $product->id,
            'name' => $product->name,
            'imageUrl' => $product->image_url,
            'isFavorite' => ClinicProductFavorite::where('clinic_id', $request->clinicId())
                ->where('product_id', $product->id)->exists(),
            'manufacturer' => $product->manufacturer?->name,
            'manufacturerSku' => $product->manufacturer_sku,
            'description' => $product->description,
            'offers' => ProductOfferSorter::sort($product->productOffers, $request->clinicId())
                ->map(function (ProductOfferModel $productOffer) use ($clinicIds) {
                    $clinic = $productOffer->clinics->whereIn('id', $clinicIds)->first();
                    $clinicPrice = $clinic?->pivot?->price;
                    $price = $productOffer->price;

                    return [
                        'id' => $productOffer->id,
                        'vendor' => [
                            'id' => $productOffer->vendor->id,
                            'name' => $productOffer->vendor->name,
                            'imageUrl' => asset("storage/{$productOffer->vendor->image_path}"),
                            'type' => $productOffer->vendor->type,
                        ],
                        'vendorSku' => $productOffer->vendor_sku,
                        'price' => ! is_null($price) ? Money::ofMinor($price, 'USD')->getAmount() : null,
                        'clinicPrice' => ! is_null($clinicPrice) ? Money::ofMinor($clinicPrice, 'USD')->getAmount() : null,
                        'stockStatus' => $productOffer->stock_status,
                        'lastOrderedAt' => $productOffer->orderItems?->first()?->created_at,
                        'lastOrderedQuantity' => $productOffer->orderItems?->first()?->quantity,
                        'increments' => $productOffer->increments ?? 1,
                        'isRecommended' => $productOffer->is_recommended ?? false,
                        'unitOfMeasure' => $productOffer->unit_of_measure,
                        'size' => $productOffer->size,
                        'raw_category_1' => $productOffer->raw_category_1,
                        'raw_category_2' => $productOffer->raw_category_2,
                        'raw_category_3' => $productOffer->raw_category_3,
                        'raw_category_4' => $productOffer->raw_category_4,
                    ];
                })
                ->filter(function ($offer) {
                    return ! is_null($offer['price']) || ! is_null($offer['clinicPrice']);
                })
                ->values()
                ->toArray(),

            'attributes' => $this->whenLoaded('alternative', function () use ($product) {
                return $product->relationLoaded('attributes') ? Attribute::collection($product->attributes) : [];
            }),

            /** Product Flags */
            'isHazardous' => $product->is_hazardous,
            'requiresPrescription' => $product->requires_prescription,
            'requiresColdShipping' => $product->requires_cold_shipping,
            'isControlledSubstance' => $product->is_controlled_substance,
            'isControlled222Form' => $product->is_controlled_222_form,
            'requiresPedigree' => $product->requires_pedigree,
            'nationalDrugCode' => $product->national_drug_code,
        ];
    }
}
