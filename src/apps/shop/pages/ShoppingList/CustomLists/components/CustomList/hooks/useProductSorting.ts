import { useMemo } from 'react';
import { ProductType } from '@/types';

interface UseProductSortingProps<T = ProductType> {
  products: T[];
  searchQuery: string;
  listName: string;
}

interface UseProductSortingReturn<T = ProductType> {
  sortedProducts: T[];
  isProductMatch: (product: T) => boolean;
}

export const useProductSorting = <T extends { id: string; name: string }>({
  products,
  searchQuery,
  listName,
}: UseProductSortingProps<T>): UseProductSortingReturn<T> => {
  const sortedProducts = useMemo(() => {
    if (!searchQuery.trim()) return products;

    const query = searchQuery.toLowerCase();
    const listNameMatches = listName.toLowerCase().includes(query);

    if (listNameMatches) return products;

    const matchingProducts: T[] = [];
    const nonMatchingProducts: T[] = [];

    products.forEach((product) => {
      const productNameMatches = product.name.toLowerCase().includes(query);
      if (productNameMatches) {
        matchingProducts.push(product);
      } else {
        nonMatchingProducts.push(product);
      }
    });

    return [...matchingProducts, ...nonMatchingProducts];
  }, [products, searchQuery, listName]);

  const isProductMatch = (product: T): boolean => {
    return !!searchQuery && product.name.toLowerCase().includes(searchQuery);
  };

  return {
    sortedProducts,
    isProductMatch,
  };
};
