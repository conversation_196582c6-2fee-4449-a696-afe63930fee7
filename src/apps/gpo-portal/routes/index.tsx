import { lazy, LazyExoticComponent } from 'react';
import { createBrowserRouter, Navigate } from 'react-router-dom';
import { AuthLayout } from '@/libs/auth/components/AuthLayout/AuthLayout';
import { DashboardLayout } from '../Layouts/DashboardLayout/DashboardLayout';
import { GPO_ROUTES_PATH } from './routes';
import { Page404 } from '@/apps/shop/pages/Page404/Page404';

const pages = [
  'Login',
  'ForgotPassword',
  'ResetPassword',
  'Dashboard',
  'SpendAnalysis',
  'Settings',
] as const;
type PageName = (typeof pages)[number];

const pageModules: Record<
  PageName,
  LazyExoticComponent<() => JSX.Element>
> = pages.reduce(
  (acc, pageName) => ({
    ...acc,
    [pageName]: lazy(() =>
      import(`@/apps/gpo-portal/pages/${pageName}/${pageName}.tsx`).then(
        (pageModule) => ({
          default: pageModule[pageName] as React.FC,
        }),
      ),
    ),
  }),
  {} as Record<PageName, LazyExoticComponent<() => JSX.Element>>,
);

const {
  Login,
  ForgotPassword,
  ResetPassword,
  Dashboard,
  SpendAnalysis,
  Settings,
} = pageModules;

export const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <AuthLayout
        bg="linear-gradient(180deg, #7a8ba8 0%, #1a3a6b 100%)"
        innerBg="#fff"
      />
    ),
    children: [
      { index: true, element: <Navigate to={GPO_ROUTES_PATH.login} /> },
      { path: GPO_ROUTES_PATH.login, element: <Login /> },
      { path: GPO_ROUTES_PATH.forgotPassword, element: <ForgotPassword /> },
      { path: GPO_ROUTES_PATH.resetPassword, element: <ResetPassword /> },
    ],
  },
  {
    path: '/',
    element: <DashboardLayout />,
    children: [
      {
        path: GPO_ROUTES_PATH.dashboard,
        element: <Dashboard />,
      },
      {
        path: GPO_ROUTES_PATH.spendAnalysis,
        element: <SpendAnalysis />,
      },
      {
        path: GPO_ROUTES_PATH.settings,
        element: <Settings />,
      },
      {
        path: '*',
        element: <Page404 />,
      },
    ],
  },
]);
