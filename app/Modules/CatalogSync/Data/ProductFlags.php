<?php

declare(strict_types=1);

namespace App\Modules\CatalogSync\Data;

use Spatie\LaravelData\Data;

final class ProductFlags extends Data
{
    public function __construct(
        public readonly bool $isHazardous = false,
        public readonly bool $requiresPrescription = false,
        public readonly bool $requiresColdShipping = false,
        public readonly bool $isControlledSubstance = false,
        public readonly bool $requiresPedigree = false,
        public readonly bool $isControlled222Form = false,
    ) {}
}
