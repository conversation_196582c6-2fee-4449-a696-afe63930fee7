<?php

declare(strict_types=1);

namespace App\Modules\Order\Data;

use Spa<PERSON>\LaravelData\Data;

final class ProductData extends Data
{
    public function __construct(
        public readonly string $id,
        public readonly string $name,
        public readonly ?string $imageUrl,
        public readonly array $offers,
        public readonly ?bool $isHazardous,
        public readonly ?bool $requiresPrescription,
        public readonly ?bool $requiresColdShipping,
        public readonly ?bool $isControlledSubstance,
        public readonly ?bool $isControlled222Form,
        public readonly ?bool $requiresPedigree,
    ) {}
}
