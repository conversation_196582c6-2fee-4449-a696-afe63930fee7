<?php

declare(strict_types=1);

namespace App\Modules\Gpo\Services;

use App\Modules\Gpo\Data\VendorOverviewData;
use Exception;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Facades\Storage;
use Rap2hpoutre\FastExcel\FastExcel;
use Symfony\Component\HttpFoundation\BinaryFileResponse;

final class VendorCsvExportService
{
    /**
     * Export vendor overview data to an XLSX file.
     *
     * @param array{total_spend: float, vendors: array<VendorOverviewData>} $vendorsOverview
     */
    public function exportToCsv(array $vendorsOverview): BinaryFileResponse
    {
        if (empty($vendorsOverview) || empty($vendorsOverview['vendors'])) {
            throw new Exception('No vendor data found for the specified date range');
        }

        $fileName = $this->generateFileName();
        $filePath = Storage::path($fileName);

        $vendorData = $this->prepareVendorData($vendorsOverview['vendors']->toArray());

        (new FastExcel($vendorData))->export($filePath);

        return Response::download($filePath, $fileName)->deleteFileAfterSend(true);
    }

    private function generateFileName(): string
    {
        return 'vendor_overview_'.now()->format('Y-m-d_H-i-s').'.xlsx';
    }

    /**
     * @param array<array> $vendors
     */
    private function prepareVendorData(array $vendors): array
    {
        $data = [];

        foreach ($vendors as $vendor) {
            $data[] = $this->prepareVendorRow($vendor);
        }

        return $data;
    }

    private function prepareVendorRow(array $vendor): array
    {
        return [
            'Vendor Name' => $vendor['name'],
            'Vendor Type' => $vendor['type'],
            'Market Share %' => number_format($vendor['marketSharePercentage'], 2),
            'Total Spend ($)' => number_format($vendor['totalSpend'], 2),
            'Growth Target %' => number_format($vendor['growthTargetPercentage'], 2),
            'Amount Until Goal ($)' => number_format($vendor['amountUntilGoal'], 2),
            'Goal Amount ($)' => number_format($vendor['goalAmount'], 2),
            'Notification' => $vendor['notification'] ?? '',
        ];
    }
}
