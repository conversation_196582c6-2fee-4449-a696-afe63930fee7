<?php

declare(strict_types=1);

namespace App\Modules\Monite\Actions;

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Enums\MoniteCounterpartSuggestionStatus;
use App\Modules\Monite\Models\MoniteCounterpartSuggestion;
use App\Modules\Monite\Models\MoniteVendorMapping;
use App\Modules\Monite\Models\MoniteWebhookEvent;
use App\Modules\Monite\Services\VendorFuzzyMatcher;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use RuntimeException;
use Throwable;

final class ProcessCounterpartWebhookAction
{
    public function __construct(
        private readonly MoniteApiClientInterface $apiClient,
        private readonly VendorFuzzyMatcher $fuzzyMatcher
    ) {}

    public function execute(MoniteWebhookEvent $webhookEvent, Clinic $clinic): void
    {
        try {
            $webhookEvent->markAsProcessing();

            $counterpartId = $webhookEvent->object_id;
            if (! $counterpartId) {
                throw new InvalidArgumentException('Counterpart ID not found in webhook payload');
            }

            $existingMapping = MoniteVendorMapping::where('monite_counterpart_id', $counterpartId)
                ->where('clinic_id', $clinic->id)
                ->first();

            if ($existingMapping) {
                Log::info('Counterpart already has a mapping, skipping', [
                    'counterpart_id' => $counterpartId,
                    'clinic_id' => $clinic->id,
                    'vendor_id' => $existingMapping->vendor_id,
                ]);

                $webhookEvent->markAsProcessed();

                return;
            }

            $counterpartData = $this->fetchCounterpartDetails($clinic, $counterpartId);

            if (! $counterpartData) {
                throw new RuntimeException('Failed to fetch counterpart details from Monite API');
            }

            $counterpartName = $counterpartData['organization']['legal_name'] ??
                              $counterpartData['name'] ?? '';

            if (empty($counterpartName)) {
                throw new InvalidArgumentException('Counterpart name is required');
            }

            $bestMatch = $this->fuzzyMatcher->findBestMatch($counterpartName);
            $this->createSuggestion($webhookEvent, $clinic, $counterpartId, $counterpartName, $bestMatch);

            Log::info('Created counterpart suggestion', [
                'counterpart_id' => $counterpartId,
                'clinic_id' => $clinic->id,
                'has_vendor_match' => $bestMatch !== null,
            ]);

            $webhookEvent->markAsProcessed();

        } catch (Throwable $e) {
            Log::error('Error processing counterpart webhook', [
                'webhook_event_id' => $webhookEvent->id,
                'clinic_id' => $clinic->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $webhookEvent->markAsFailed("Processing error: {$e->getMessage()}");
            throw $e;
        }
    }

    private function fetchCounterpartDetails(Clinic $clinic, string $counterpartId): ?array
    {
        try {
            $client = $this->apiClient->withEntityId($clinic->monite_entity_id);
            $response = $client->get("/counterparts/{$counterpartId}");

            return $response->json();
        } catch (Throwable $e) {
            Log::error('Failed to fetch counterpart details from Monite API', [
                'counterpart_id' => $counterpartId,
                'clinic_id' => $clinic->id,
                'error' => $e->getMessage(),
            ]);

            return null;
        }
    }

    private function createSuggestion(
        MoniteWebhookEvent $webhookEvent,
        Clinic $clinic,
        string $counterpartId,
        string $counterpartName,
        ?Vendor $vendor = null
    ): void {
        MoniteCounterpartSuggestion::create([
            'clinic_id' => $clinic->id,
            'monite_counterpart_id' => $counterpartId,
            'monite_counterpart_name' => $counterpartName,
            'suggested_vendor_id' => $vendor?->id,
            'suggested_vendor_name' => $vendor?->name,
            'status' => MoniteCounterpartSuggestionStatus::PENDING,
            'webhook_event_id' => $webhookEvent->id,
        ]);
    }
}
