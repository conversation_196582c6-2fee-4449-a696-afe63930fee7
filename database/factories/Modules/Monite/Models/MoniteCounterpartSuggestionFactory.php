<?php

declare(strict_types=1);

namespace Database\Factories\Modules\Monite\Models;

use App\Models\Clinic;
use App\Models\User;
use App\Models\Vendor;
use App\Modules\Monite\Enums\MoniteCounterpartSuggestionStatus;
use App\Modules\Monite\Models\MoniteCounterpartSuggestion;
use App\Modules\Monite\Models\MoniteWebhookEvent;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Modules\Monite\Models\MoniteCounterpartSuggestion>
 */
final class MoniteCounterpartSuggestionFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var class-string<MoniteCounterpartSuggestion>
     */
    protected $model = MoniteCounterpartSuggestion::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'clinic_id' => Clinic::factory(),
            'monite_counterpart_id' => $this->faker->uuid(),
            'monite_counterpart_name' => $this->faker->company(),
            'suggested_vendor_id' => Vendor::factory(),
            'suggested_vendor_name' => $this->faker->company(),
            'status' => MoniteCounterpartSuggestionStatus::PENDING,
            'webhook_event_id' => MoniteWebhookEvent::factory(),
        ];
    }

    /**
     * Indicate that the suggestion is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => MoniteCounterpartSuggestionStatus::APPROVED,
            'approved_by' => User::factory(),
            'approved_at' => $this->faker->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the suggestion is rejected.
     */
    public function rejected(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => MoniteCounterpartSuggestionStatus::REJECTED,
            'rejected_reason' => $this->faker->sentence(),
        ]);
    }

    /**
     * Indicate that the suggestion is for manual vendor creation.
     */
    public function manualCreation(): static
    {
        return $this->state(fn (array $attributes) => [
            'suggested_vendor_id' => null,
            'suggested_vendor_name' => null,
        ]);
    }
}
