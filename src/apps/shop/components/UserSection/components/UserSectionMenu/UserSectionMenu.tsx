import { SHOP_ROUTES_PATH } from '@/apps/shop/routes/routes';
import { useClinicStore } from '@/apps/shop/stores/useClinicStore';
import { useAuthStore } from '@/apps/shop/stores/useAuthStore';
import { Menu, MenuItem } from '@/libs/ui/Menu/Menu';
import { Button } from '@/libs/ui/Button/Button';
import { useNavigate } from 'react-router-dom';
import LockIcon from './assets/lock.svg?react';
import { MODAL_NAME } from '@/constants';
import { useModalStore } from '@/apps/shop/stores/useModalStore';
import { ChangePasswordModal } from '../ChangePasswordModal/ChangePasswordModal';
import { Icon } from '@/libs/icons/Icon';

export const UserSectionMenu = () => {
  const { user, logout, leaveImpersonation } = useAuthStore();
  const { clearClinic } = useClinicStore();
  const navigate = useNavigate();
  const { openModal } = useModalStore();

  const handleLogout = () => {
    logout();
    clearClinic();
    navigate(SHOP_ROUTES_PATH.login);
  };

  const handleChangePassword = () => {
    openModal({ name: MODAL_NAME.CHANGE_PASSWORD });
  };

  const handleLeaveImpersonation = () => {
    leaveImpersonation();
  };

  return (
    <>
      <ChangePasswordModal />
      <Menu
        trigger={
          <Button variant="unstyled" aria-label="User menu options">
            <Icon name="moreOptions" color="#333" aria-hidden={true} />
          </Button>
        }
        side="bottom"
        align="end"
        size="lg"
      >
        <MenuItem onClick={handleChangePassword}>
          <div className="flex items-center">
            <LockIcon className="mr-2" />
            Change password
          </div>
        </MenuItem>
        {user?.isImpersonating ? (
          <MenuItem onClick={handleLeaveImpersonation}>
            <div className="flex items-center">
              <Icon name="exit" className="mr-2" />
              Leave Impersonation
            </div>
          </MenuItem>
        ) : (
          <MenuItem onClick={handleLogout}>
            <div className="flex items-center">
              <Icon name="exit" className="mr-2" />
              Logout
            </div>
          </MenuItem>
        )}
      </Menu>
    </>
  );
};
