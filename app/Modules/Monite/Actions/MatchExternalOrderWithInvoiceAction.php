<?php

declare(strict_types=1);

namespace App\Modules\Monite\Actions;

use App\Modules\Monite\Models\MoniteInvoice;
use App\Modules\Order\Models\ExternalOrder;
use Illuminate\Support\Facades\Log;

final class MatchExternalOrderWithInvoiceAction
{
    /**
     * Match external order with invoice based on invoice_number and vendor_id.
     */
    public function execute(MoniteInvoice $moniteInvoice): void
    {
        if (! $moniteInvoice->invoice_number || ! $moniteInvoice->vendor_id) {
            return;
        }

        // Find any existing external order with the same invoice_number and vendor_id
        $externalOrder = ExternalOrder::where('invoice_number', $moniteInvoice->invoice_number)
            ->whereHas('subOrder', function ($query) use ($moniteInvoice) {
                $query->where('vendor_id', $moniteInvoice->vendor_id);
            })
            ->first();

        if ($externalOrder) {
            // Update the invoice with the external_order_id
            $moniteInvoice->update(['external_order_id' => $externalOrder->id]);

            Log::info('External order matched with invoice', [
                'invoice_id' => $moniteInvoice->id,
                'external_order_id' => $externalOrder->id,
                'invoice_number' => $moniteInvoice->invoice_number,
                'vendor_id' => $moniteInvoice->vendor_id,
            ]);
        } else {
            Log::info('No matching external order found for invoice', [
                'invoice_id' => $moniteInvoice->id,
                'invoice_number' => $moniteInvoice->invoice_number,
                'vendor_id' => $moniteInvoice->vendor_id,
            ]);
        }
    }
}
