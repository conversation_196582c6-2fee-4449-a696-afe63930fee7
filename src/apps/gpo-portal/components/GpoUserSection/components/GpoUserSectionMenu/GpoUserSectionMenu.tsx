import { Menu, MenuItem } from '@/libs/ui/Menu/Menu';
import { Icon } from '@/libs/icons/Icon';
import { useAuthStore } from '@/apps/gpo-portal/stores/useAuthStore';
import { Button } from '@/libs/ui/Button/Button';

export const GpoUserSectionMenu = () => {
  const { logout } = useAuthStore();

  return (
    // TODO: Add change password modal
    <Menu
      trigger={
        <Button variant="unstyled" aria-label="User menu options">
          <Icon name="moreOptions" color="#333" aria-hidden={true} />
        </Button>
      }
      side="bottom"
      align="end"
      size="lg"
    >
      <MenuItem onClick={logout} className="text-red-600">
        <div className="flex items-center">
          <Icon name="exit" size={16} className="mr-2" />
          Logout
        </div>
      </MenuItem>
    </Menu>
  );
};
