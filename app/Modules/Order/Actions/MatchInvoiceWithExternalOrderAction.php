<?php

declare(strict_types=1);

namespace App\Modules\Order\Actions;

use App\Modules\Monite\Models\MoniteInvoice;
use App\Modules\Order\Models\ExternalOrder;
use Illuminate\Support\Facades\Log;

final class MatchInvoiceWithExternalOrderAction
{
    /**
     * Match invoice with external order based on invoice_number and vendor_id.
     */
    public function execute(ExternalOrder $externalOrder): void
    {
        $vendorId = $externalOrder->subOrder->vendor_id;

        // Find any existing invoice with the same invoice_number and vendor_id
        $invoice = MoniteInvoice::where('invoice_number', $externalOrder->invoice_number)
            ->where('vendor_id', $vendorId)
            ->whereNull('external_order_id')
            ->first();

        if ($invoice) {
            // Update the invoice with the external_order_id
            $invoice->update(['external_order_id' => $externalOrder->id]);

            Log::info('Invoice matched with external order', [
                'invoice_id' => $invoice->id,
                'external_order_id' => $externalOrder->id,
                'invoice_number' => $externalOrder->invoice_number,
                'vendor_id' => $vendorId,
            ]);
        } else {
            Log::info('No matching invoice found for external order', [
                'external_order_id' => $externalOrder->id,
                'invoice_number' => $externalOrder->invoice_number,
                'vendor_id' => $vendorId,
            ]);
        }
    }
}
