<?php

declare(strict_types=1);

namespace App\Modules\Monite\Services;

use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Throwable;

class MoniteWebhookVerificationService
{
    /**
     * Time tolerance in seconds for webhook signature timestamp
     */
    private const TIMESTAMP_TOLERANCE_SECONDS = 300; // 5 minutes

    /**
     * Verify the Monite webhook signature
     *
     * Monite webhook signatures are formatted as t=timestamp,v1=signature
     * where the signature is an HMAC-SHA256 hash of the timestamp + '.' + payload
     * using the webhook secret as the key
     */
    public function verifySignature(?string $signatureHeader, string $payload, ?string $objectType = null): bool
    {
        // For testing purposes, bypass signature verification if no secret is configured
        $webhookSecret = $this->getWebhookSecret($objectType);
        if (empty($webhookSecret)) {
            Log::warning('Monite webhook secret not configured - bypassing signature verification for testing', [
                'object_type' => $objectType,
            ]);

            return true;
        }

        if (empty($signatureHeader)) {
            Log::warning('Empty signature header in Monite webhook');

            return false;
        }

        try {
            // Parse the signature header (format: t=timestamp,v1=signature)
            $parts = explode(',', $signatureHeader);

            if (count($parts) < 2) {
                Log::warning('Invalid signature format', ['signature' => $signatureHeader]);

                return false;
            }

            $timestampPart = null;
            $signaturePart = null;

            foreach ($parts as $part) {
                if (str_starts_with($part, 't=')) {
                    $timestampPart = mb_substr($part, 2);
                } elseif (str_starts_with($part, 'v1=')) {
                    $signaturePart = mb_substr($part, 3);
                }
            }

            if (! $timestampPart || ! $signaturePart) {
                Log::warning('Missing timestamp or signature', ['signature' => $signatureHeader]);

                return false;
            }

            // Verify the timestamp is recent
            $timestamp = (int) $timestampPart;
            $now = time();

            if (abs($now - $timestamp) > self::TIMESTAMP_TOLERANCE_SECONDS) {
                Log::warning('Webhook timestamp too old or from future', [
                    'webhook_time' => $timestamp,
                    'current_time' => $now,
                    'difference' => abs($now - $timestamp),
                ]);

                return false;
            }

            // Calculate expected signature
            $signedContent = "{$timestampPart}.{$payload}";
            $expectedSignature = hash_hmac('sha256', $signedContent, $webhookSecret);

            // Compare signatures using constant-time comparison
            return hash_equals($expectedSignature, $signaturePart);
        } catch (Throwable $e) {
            Log::error('Error verifying Monite webhook signature', [
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get the webhook secret from config for a specific object type
     */
    private function getWebhookSecret(?string $objectType = null): ?string
    {
        if ($objectType) {
            return Config::get("monite.webhook_secrets.{$objectType}");
        }

        // Fallback to the first available secret for backward compatibility
        $secrets = Config::get('monite.webhook_secrets', []);

        return reset($secrets) ?: null;
    }
}
