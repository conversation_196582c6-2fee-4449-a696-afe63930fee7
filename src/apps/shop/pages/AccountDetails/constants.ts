import i18n from '@/libs/i18n';
import * as Yup from 'yup';

export const SCHEMA = Yup.object().shape({
  name: Yup.string().required(() =>
    i18n.t('form.errorMessage.required', {
      path: 'Account Name',
    }),
  ),
  ein: Yup.string()
    .matches(
      /^\d{2}-\d{7}$/,
      i18n.t('form.errorMessage.taxIdFormat', {
        path: i18n.t('onboarding.step1.ein'),
      }),
    )
    .required(() =>
      i18n.t('form.errorMessage.required', {
        path: i18n.t('onboarding.step1.ein'),
      }),
    ),
  phoneNumber: Yup.string().required(() =>
    i18n.t('form.errorMessage.required', {
      path: i18n.t('onboarding.step1.phoneNumber'),
    }),
  ),
});
