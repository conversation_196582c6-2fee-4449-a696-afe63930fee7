<?php

declare(strict_types=1);

namespace App\Modules\Monite\Nova\Actions;

use App\Enums\ExpenseCategory;
use App\Enums\VendorType;
use App\Models\Vendor;
use App\Modules\Monite\Enums\MoniteCounterpartSuggestionStatus;
use App\Modules\Monite\Models\MoniteVendorMapping;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use <PERSON>vel\Nova\Fields\ActionFields;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

final class RejectCounterpartSuggestion extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Reject & Create';

    public function handle(ActionFields $fields, Collection $models): mixed
    {
        $createdCount = 0;
        $errors = [];

        $reason = $fields->get('reason', 'No reason provided');

        foreach ($models as $suggestion) {
            try {
                if ($suggestion->status !== MoniteCounterpartSuggestionStatus::PENDING) {
                    $errors[] = "Suggestion for '{$suggestion->monite_counterpart_name}' is not pending";

                    continue;
                }

                $vendor = $this->createVendorFromCounterpart($suggestion, $fields);
                $this->createVendorMapping($suggestion, $vendor);

                $suggestion->markAsRejected($reason);
                $createdCount++;

            } catch (Exception $e) {
                $errors[] = "Error creating vendor for '{$suggestion->monite_counterpart_name}': {$e->getMessage()}";
            }
        }

        if ($createdCount > 0) {
            $message = "Successfully created {$createdCount} new vendor(s) and mapping(s)";
            if (! empty($errors)) {
                $message .= '. Errors: '.implode('; ', $errors);
            }

            return ActionResponse::message($message);
        }

        return ActionResponse::danger(empty($errors) ? 'No vendors were created' : implode('; ', $errors));
    }

    public function fields(NovaRequest $request): array
    {
        return [
            Textarea::make('Reason', 'reason')
                ->help('Provide a reason for rejecting the suggested match and creating a new vendor')
                ->rules('required', 'string', 'max:1000')
                ->rows(3),

            Select::make('Type', 'type')
                ->options([
                    VendorType::Distributor->value => 'Distributor',
                    VendorType::Manufacturer->value => 'Manufacturer',
                ])
                ->default(VendorType::Distributor->value)
                ->rules('required'),

            Select::make('Expense Category', 'expense_category')
                ->options([
                    ExpenseCategory::COGS->value => 'COGS',
                    ExpenseCategory::GA->value => 'GA',
                ])
                ->default(ExpenseCategory::COGS->value)
                ->rules('required'),
        ];
    }

    private function createVendorFromCounterpart($suggestion, $fields): Vendor
    {
        // Use the counterpart name and auto-generate key and slug
        $vendorName = $suggestion->monite_counterpart_name;
        $vendorKey = $this->generateVendorKey($vendorName);
        $vendorSlug = $this->generateVendorSlug($vendorName);

        return Vendor::create([
            'name' => $vendorName,
            'key' => $vendorKey,
            'slug' => $vendorSlug,
            'type' => $fields->get('type', VendorType::Distributor),
            'expense_category' => $fields->get('expense_category', ExpenseCategory::COGS),
            'is_enabled' => false,
        ]);
    }

    private function createVendorMapping($suggestion, Vendor $vendor): void
    {
        MoniteVendorMapping::create([
            'clinic_id' => $suggestion->clinic_id,
            'vendor_id' => $vendor->id,
            'monite_counterpart_id' => $suggestion->monite_counterpart_id,
        ]);
    }

    private function generateVendorKey(string $name): string
    {
        $baseKey = mb_strtoupper(preg_replace('/[^A-Za-z0-9]/', '', $name));
        $key = mb_substr($baseKey, 0, 20); // Limit to 20 characters

        // Ensure uniqueness
        $counter = 1;
        $originalKey = $key;

        while (Vendor::where('key', $key)->exists()) {
            $key = $originalKey.$counter;
            $counter++;
        }

        return $key;
    }

    private function generateVendorSlug(string $name): string
    {
        $slug = Str::slug($name);
        $slug = mb_substr($slug, 0, 255); // Limit to 255 characters

        // Ensure uniqueness
        $counter = 1;
        $originalSlug = $slug;

        while (Vendor::where('slug', $slug)->exists()) {
            $slug = $originalSlug.'-'.$counter;
            $counter++;
        }

        return $slug;
    }
}
