<?php

declare(strict_types=1);

namespace App\Models;

use App\Observers\ProductAlternativeObserver;
use Illuminate\Database\Eloquent\Attributes\ObservedBy;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

#[ObservedBy([ProductAlternativeObserver::class])]
final class ProductAlternative extends Pivot
{
    use HasUuids;

    protected $table = 'product_alternatives';

    protected $fillable = [
        'product_id',
        'alternative_id',
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    public function alternative(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'alternative_id');
    }
}
