import { useState } from 'react';
import { Button } from '@/libs/ui/Button/Button';
import { Input } from '@/libs/form/Input';
import { ColorPicker } from '@/libs/ui/ColorPicker/ColorPicker';
import { ProductDisplayCard } from '../ProductDisplayCard/ProductDisplayCard';
import { Tabs } from '@/libs/ui/Tabs/Tabs';
import { Modal } from '@/components';
import { MODAL_NAME } from '@/constants';
import {
  useModalStore,
  type ModalOptionProps,
} from '@/apps/shop/stores/useModalStore';
import { ProductType } from '@/types';

type AddProductToListModalOptions = ModalOptionProps & {
  product: ProductType;
  selectedOfferId?: string;
};

export const AddProductToListModal = () => {
  const { modalOption, closeModal } = useModalStore();
  const { product, selectedOfferId } =
    modalOption as AddProductToListModalOptions;

  const [activeTab, setActiveTab] = useState(1); // 0 = existing, 1 = new
  const [listName, setListName] = useState('My Shopping List 4');
  const [selectedColor, setSelectedColor] = useState('#EF4444');
  const [label, setLabel] = useState('Shop Urgent!!');
  const [isLoading, setIsLoading] = useState(false);

  const handleCreateList = async () => {
    setIsLoading(true);

    try {
      // TODO: Implement actual list creation logic
      console.log('Creating list:', {
        mode: activeTab === 0 ? 'existing' : 'new',
        listName,
        color: selectedColor,
        label,
        product,
        selectedOfferId,
      });

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      closeModal();
    } catch (error) {
      console.error('Failed to create list:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const tabs = [
    {
      label: 'Add to Existing List',
      onClick: (index: number) => setActiveTab(index),
    },
    {
      label: 'Create a New List',
      onClick: (index: number) => setActiveTab(index),
    },
  ];

  if (!product) {
    return null;
  }

  return (
    <Modal
      name={MODAL_NAME.ADD_PRODUCT_TO_LIST}
      withCloseButton
      size="523px"
      customClasses={{
        header: styles.outerModalHeader,
        body: styles.outerModalBody,
        content: styles.outerModalContent,
      }}
    >
      <div className="max-w-md">
        {/* Header */}
        <div className="mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            Add Product to List
          </h2>
        </div>

        {/* Content */}
        <div className="space-y-6">
          {/* Description */}
          <p className="text-center text-gray-600">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit.
          </p>

          {/* Tabs */}
          <div>
            <Tabs active={activeTab} tabs={tabs} />
          </div>

          {/* Tab Content */}
          <div className="mt-4">
            {activeTab === 0 && (
              <div className="py-8 text-center">
                <p className="text-gray-600">
                  Add to Existing List functionality coming soon...
                </p>
              </div>
            )}

            {activeTab === 1 && (
              <div className="space-y-4">
                {/* List Name Input */}
                <div>
                  <Input
                    label="List name"
                    value={listName}
                    onChange={(e) => setListName(e.target.value)}
                    placeholder="Enter list name"
                  />
                </div>

                {/* Product Display */}
                <ProductDisplayCard
                  product={product}
                  selectedOfferId={selectedOfferId}
                />

                {/* Color and Label Row */}
                <div className="grid grid-cols-2 gap-4">
                  <ColorPicker
                    label="Add color"
                    value={selectedColor}
                    onChange={setSelectedColor}
                  />

                  <div>
                    <Input
                      label="Add label"
                      value={label}
                      onChange={(e) => setLabel(e.target.value)}
                      placeholder="Enter label"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        {activeTab === 1 && (
          <div className="mt-6 border-t border-gray-200 pt-6">
            <Button
              onClick={handleCreateList}
              loading={isLoading}
              disabled={!listName.trim()}
              className="w-full"
            >
              Create List
            </Button>
          </div>
        )}
      </div>
    </Modal>
  );
};
