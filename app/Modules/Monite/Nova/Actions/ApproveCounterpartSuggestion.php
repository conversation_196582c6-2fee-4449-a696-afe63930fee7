<?php

declare(strict_types=1);

namespace App\Modules\Monite\Nova\Actions;

use App\Modules\Monite\Enums\MoniteCounterpartSuggestionStatus;
use App\Modules\Monite\Models\MoniteVendorMapping;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Actions\ActionResponse;
use Laravel\Nova\Fields\ActionFields;

final class ApproveCounterpartSuggestion extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Approve Match';

    public function handle(ActionFields $fields, Collection $models): mixed
    {
        $approvedCount = 0;
        $errors = [];

        foreach ($models as $suggestion) {
            try {
                if ($suggestion->status !== MoniteCounterpartSuggestionStatus::PENDING) {
                    $errors[] = "Suggestion for '{$suggestion->monite_counterpart_name}' is not pending";

                    continue;
                }

                if ($suggestion->suggested_vendor_id) {
                    $vendor = $suggestion->suggestedVendor;
                    if (! $vendor) {
                        $errors[] = "Vendor '{$suggestion->suggested_vendor_name}' is no longer available";

                        continue;
                    }

                    // Check if vendor already has a mapping for this clinic
                    $existingMapping = MoniteVendorMapping::where('vendor_id', $vendor->id)
                        ->where('clinic_id', $suggestion->clinic_id)
                        ->first();

                    if ($existingMapping) {
                        $errors[] = "Vendor '{$vendor->name}' already has a Monite mapping for this clinic";

                        continue;
                    }

                    MoniteVendorMapping::create([
                        'vendor_id' => $vendor->id,
                        'clinic_id' => $suggestion->clinic_id,
                        'monite_counterpart_id' => $suggestion->monite_counterpart_id,
                    ]);
                }

                $suggestion->markAsApproved(auth()->id());

                $approvedCount++;

            } catch (Exception $e) {
                $errors[] = "Error approving suggestion for '{$suggestion->monite_counterpart_name}': {$e->getMessage()}";
            }
        }

        if ($approvedCount > 0) {
            $message = "Successfully approved {$approvedCount} suggestion(s)";
            if (! empty($errors)) {
                $message .= '. Errors: '.implode('; ', $errors);
            }

            return ActionResponse::message($message);
        }

        return ActionResponse::danger(empty($errors) ? 'No suggestions were approved' : implode('; ', $errors));
    }
}
