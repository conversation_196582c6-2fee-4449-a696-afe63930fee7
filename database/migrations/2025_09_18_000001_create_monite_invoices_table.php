<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('monite_invoices', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->foreignUuid('clinic_id')->constrained()->cascadeOnDelete();
            $table->foreignUuid('vendor_id')->nullable()->constrained()->nullOnDelete();
            $table->foreignUuid('external_order_id')->nullable()->constrained('external_orders')->nullOnDelete();
            $table->string('invoice_number')->nullable()->index();
            $table->string('status')->default('pending')->index();
            $table->decimal('amount', 10, 2)->default(0);
            $table->string('currency', 3)->default('USD');
            $table->date('due_date')->nullable();
            $table->string('vendor_name')->nullable();
            $table->string('monite_payable_id')->nullable()->unique()->index();
            $table->timestamps();

            // Basic indexes
            $table->index(['clinic_id', 'status']);
            $table->index(['clinic_id', 'monite_payable_id']);
        });
    }
};
