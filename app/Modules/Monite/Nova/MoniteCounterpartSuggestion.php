<?php

declare(strict_types=1);

namespace App\Modules\Monite\Nova;

use App\Modules\Account\Nova\User;
use App\Modules\Monite\Enums\MoniteCounterpartSuggestionStatus;
use App\Modules\Monite\Nova\Actions\ApproveCounterpartSuggestion;
use App\Modules\Monite\Nova\Actions\RejectCounterpartSuggestion;
use App\Modules\Monite\Nova\Filters\CounterpartSuggestionStatusFilter;
use App\Nova\Clinic;
use App\Nova\Resource;
use App\Nova\Vendor;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\DateTime;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Fields\Textarea;
use Laravel\Nova\Http\Requests\NovaRequest;

final class MoniteCounterpartSuggestion extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Modules\Monite\Models\MoniteCounterpartSuggestion>
     */
    public static $model = \App\Modules\Monite\Models\MoniteCounterpartSuggestion::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'monite_counterpart_name';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'monite_counterpart_name',
        'suggested_vendor_name',
    ];

    public static function label(): string
    {
        return 'Counterpart Suggestions';
    }

    public static function authorizedToCreate(Request $request): bool
    {
        return false;
    }

    public function fields(NovaRequest $request): array
    {
        return [
            ID::make()->sortable(),

            BelongsTo::make('Clinic', 'clinic', Clinic::class)
                ->sortable()
                ->showOnPreview(),

            Text::make('Monite Counterpart Name', 'monite_counterpart_name')
                ->sortable()
                ->showOnPreview()
                ->readonly(),

            Text::make('Monite Counterpart ID', 'monite_counterpart_id')
                ->onlyOnDetail()
                ->readonly(),

            BelongsTo::make('Suggested Vendor', 'suggestedVendor', Vendor::class)
                ->nullable()
                ->sortable()
                ->showOnPreview()
                ->readonly(),

            Text::make('Suggested Vendor Name', 'suggested_vendor_name')
                ->nullable()
                ->sortable()
                ->showOnPreview()
                ->readonly(),

            Select::make('Status', 'status')
                ->options(MoniteCounterpartSuggestionStatus::options())
                ->displayUsingLabels()
                ->sortable()
                ->showOnPreview(),

            BelongsTo::make('Approved By', 'approvedBy', User::class)
                ->nullable()
                ->sortable()
                ->showOnPreview()
                ->readonly(),

            DateTime::make('Approved At', 'approved_at')
                ->nullable()
                ->sortable()
                ->showOnPreview()
                ->readonly(),

            Textarea::make('Rejected Reason', 'rejected_reason')
                ->nullable()
                ->showOnPreview()
                ->readonly(),
        ];
    }

    public function actions(NovaRequest $request): array
    {
        return [
            (new ApproveCounterpartSuggestion),
            (new RejectCounterpartSuggestion),
        ];
    }

    public function filters(NovaRequest $request): array
    {
        return [
            new CounterpartSuggestionStatusFilter(),
        ];
    }

    public function authorizedToUpdate(Request $request): bool
    {
        return false;
    }

    public function authorizedToDelete(Request $request): bool
    {
        return false;
    }
}
