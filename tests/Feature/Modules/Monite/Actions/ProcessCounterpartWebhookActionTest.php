<?php

declare(strict_types=1);

use App\Models\Clinic;
use App\Models\Vendor;
use App\Modules\Monite\Actions\ProcessCounterpartWebhookAction;
use App\Modules\Monite\Contracts\MoniteApiClientInterface;
use App\Modules\Monite\Models\MoniteCounterpartSuggestion;
use App\Modules\Monite\Models\MoniteVendorMapping;
use App\Modules\Monite\Models\MoniteWebhookEvent;
use App\Modules\Monite\Services\VendorFuzzyMatcher;
use Illuminate\Http\Client\Response;

beforeEach(function () {
    $this->clinic = Clinic::factory()->create(['monite_entity_id' => fake()->uuid()]);
    $this->counterpartId = fake()->uuid();
    $this->webhookEvent = MoniteWebhookEvent::factory()->create([
        'object_id' => $this->counterpartId,
        'entity_id' => $this->clinic->monite_entity_id,
        'event_type' => 'counterpart.created',
    ]);

    $this->mockApiClient = $this->mock(MoniteApiClientInterface::class);
    $this->mockFuzzyMatcher = $this->mock(VendorFuzzyMatcher::class);

    $this->action = new ProcessCounterpartWebhookAction(
        $this->mockApiClient,
        $this->mockFuzzyMatcher
    );
});

it('processes counterpart webhook when mapping already exists', function () {
    // Create existing mapping
    $vendor = Vendor::factory()->create();
    MoniteVendorMapping::factory()->create([
        'vendor_id' => $vendor->id,
        'clinic_id' => $this->clinic->id,
        'monite_counterpart_id' => $this->counterpartId,
    ]);

    $this->action->execute($this->webhookEvent, $this->clinic);

    $this->webhookEvent->refresh();
    expect($this->webhookEvent->status->value)->toBe('processed');

    // No suggestions should be created
    expect(MoniteCounterpartSuggestion::count())->toBe(0);
});

it('creates suggestions when vendor matches are found', function () {
    $vendor1 = Vendor::factory()->create(['name' => 'Acme Corp']);
    $vendor2 = Vendor::factory()->create(['name' => 'Acme Corporation']);

    $mockClient = $this->mock(MoniteApiClientInterface::class);
    $mockClient->shouldReceive('withEntityId')
        ->with($this->clinic->monite_entity_id)
        ->andReturnSelf();

    $mockResponse = $this->mock(Response::class);
    $mockResponse->shouldReceive('json')->andReturn([
        'id' => $this->counterpartId,
        'name' => 'Acme Corporation',
    ]);

    $mockClient->shouldReceive('get')
        ->with("/counterparts/{$this->counterpartId}")
        ->andReturn($mockResponse);

    $this->mockFuzzyMatcher
        ->shouldReceive('findBestMatch')
        ->with('Acme Corporation')
        ->andReturn($vendor2);

    $action = new ProcessCounterpartWebhookAction($mockClient, $this->mockFuzzyMatcher);
    $action->execute($this->webhookEvent, $this->clinic);

    $this->webhookEvent->refresh();
    expect($this->webhookEvent->status->value)->toBe('processed');

    // One suggestion should be created
    expect(MoniteCounterpartSuggestion::count())->toBe(1);

    $suggestion = MoniteCounterpartSuggestion::first();
    expect($suggestion->suggested_vendor_id)->toBe($vendor2->id);
});

it('creates manual creation suggestion when no matches found', function () {
    $mockClient = $this->mock(MoniteApiClientInterface::class);
    $mockClient->shouldReceive('withEntityId')
        ->with($this->clinic->monite_entity_id)
        ->andReturnSelf();

    $mockResponse = $this->mock(Response::class);
    $mockResponse->shouldReceive('json')->andReturn([
        'id' => $this->counterpartId,
        'name' => 'Unique Vendor Name',
    ]);

    $mockClient->shouldReceive('get')
        ->with("/counterparts/{$this->counterpartId}")
        ->andReturn($mockResponse);

    $this->mockFuzzyMatcher
        ->shouldReceive('findBestMatch')
        ->with('Unique Vendor Name')
        ->andReturn(null);

    $action = new ProcessCounterpartWebhookAction($mockClient, $this->mockFuzzyMatcher);
    $action->execute($this->webhookEvent, $this->clinic);

    $this->webhookEvent->refresh();
    expect($this->webhookEvent->status->value)->toBe('processed');

    // One manual creation suggestion should be created
    expect(MoniteCounterpartSuggestion::count())->toBe(1);

    $suggestion = MoniteCounterpartSuggestion::first();
    expect($suggestion->suggested_vendor_id)->toBeNull();
    expect($suggestion->monite_counterpart_name)->toBe('Unique Vendor Name');
});

it('handles api client errors gracefully', function () {
    $mockClient = $this->mock(MoniteApiClientInterface::class);
    $mockClient->shouldReceive('withEntityId')
        ->with($this->clinic->monite_entity_id)
        ->andReturnSelf();

    $mockClient->shouldReceive('get')
        ->with("/counterparts/{$this->counterpartId}")
        ->andThrow(new Exception('API Error'));

    $action = new ProcessCounterpartWebhookAction($mockClient, $this->mockFuzzyMatcher);

    expect(fn () => $action->execute($this->webhookEvent, $this->clinic))
        ->toThrow(RuntimeException::class, 'Failed to fetch counterpart details from Monite API');

    $this->webhookEvent->refresh();
    expect($this->webhookEvent->status->value)->toBe('failed');
});

it('handles missing counterpart name', function () {
    $mockClient = $this->mock(MoniteApiClientInterface::class);
    $mockClient->shouldReceive('withEntityId')
        ->with($this->clinic->monite_entity_id)
        ->andReturnSelf();

    $mockResponse = $this->mock(Response::class);
    $mockResponse->shouldReceive('json')->andReturn([
        'id' => $this->counterpartId,
    ]);

    $mockClient->shouldReceive('get')
        ->with("/counterparts/{$this->counterpartId}")
        ->andReturn($mockResponse);

    $action = new ProcessCounterpartWebhookAction($mockClient, $this->mockFuzzyMatcher);

    expect(fn () => $action->execute($this->webhookEvent, $this->clinic))
        ->toThrow(InvalidArgumentException::class, 'Counterpart name is required');

    $this->webhookEvent->refresh();
    expect($this->webhookEvent->status->value)->toBe('failed');
});
