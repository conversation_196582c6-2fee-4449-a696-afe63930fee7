import { Tooltip } from '@/libs/ui/Tooltip';
import { Button } from '@/libs/ui/Button/Button';
import { Icon } from '@/libs/icons/Icon';

interface HelpTooltipProps {
  color?: string;
  message: string;
  size?: string;
}

export const HelpTooltip = ({
  color = '#25B7D3',
  message,
  size = '0.8rem',
}: HelpTooltipProps) => (
  <Tooltip label={message} side="right" align="start">
    <Button variant="unstyled" type="button">
      <Icon name="question" color={color} size={size} />
    </Button>
  </Tooltip>
);
