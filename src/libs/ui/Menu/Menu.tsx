import { ReactNode, forwardRef } from 'react';
import * as DropdownMenu from '@radix-ui/react-dropdown-menu';
import { cva, type VariantProps } from 'class-variance-authority';
import { mergeClasses } from '@/utils/tailwind';

const menuContentVariants = cva(
  'z-50 rounded-md border bg-white shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
  {
    variants: {
      size: {
        sm: 'min-w-[120px] px-1 py-1',
        md: 'min-w-[140px] px-1 py-1',
        lg: 'min-w-[160px] px-2 py-2',
      },
    },
    defaultVariants: {
      size: 'md',
    },
  },
);

const menuItemVariants = cva(
  'cursor-pointer rounded-sm outline-none transition-colors focus:bg-gray-50 hover:bg-gray-50 data-[highlighted]:bg-gray-50',
  {
    variants: {
      size: {
        sm: 'px-1.5 py-1.5 text-xs',
        md: 'px-2 py-2 text-sm',
        lg: 'px-3 py-2.5 text-base',
      },
    },
    defaultVariants: {
      size: 'md',
    },
  },
);

export interface MenuItemProps {
  children: ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  className?: string;
}

export interface MenuProps extends VariantProps<typeof menuContentVariants> {
  children: ReactNode;
  trigger: ReactNode;
  // Positioning props
  side?: 'top' | 'right' | 'bottom' | 'left';
  align?: 'start' | 'center' | 'end';
  sideOffset?: number;
  alignOffset?: number;
  // Behavior props
  modal?: boolean;
  onOpenChange?: (open: boolean) => void;
  className?: string;
}

const MenuItem = forwardRef<
  HTMLDivElement,
  MenuItemProps & VariantProps<typeof menuItemVariants>
>(({ children, onClick, disabled = false, className, size, ...props }, ref) => {
  return (
    <DropdownMenu.Item
      ref={ref}
      className={mergeClasses(menuItemVariants({ size }), className)}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </DropdownMenu.Item>
  );
});

MenuItem.displayName = 'MenuItem';

const MenuSeparator = forwardRef<HTMLDivElement, { className?: string }>(
  ({ className, ...props }, ref) => {
    return (
      <DropdownMenu.Separator
        ref={ref}
        className={mergeClasses('my-1 h-px bg-gray-200', className)}
        {...props}
      />
    );
  },
);

MenuSeparator.displayName = 'MenuSeparator';

const MenuLabel = forwardRef<
  HTMLDivElement,
  { children: ReactNode; className?: string }
>(({ children, className, ...props }, ref) => {
  return (
    <DropdownMenu.Label
      ref={ref}
      className={mergeClasses(
        'px-2 py-1.5 text-xs font-semibold text-gray-500',
        className,
      )}
      {...props}
    >
      {children}
    </DropdownMenu.Label>
  );
});

MenuLabel.displayName = 'MenuLabel';

export const Menu = forwardRef<HTMLDivElement, MenuProps>(
  (
    {
      children,
      trigger,
      side = 'bottom',
      align = 'end',
      sideOffset = 2,
      alignOffset,
      modal = true,
      onOpenChange,
      size,
      className,
      ...props
    },
    ref,
  ) => {
    return (
      <DropdownMenu.Root modal={modal} onOpenChange={onOpenChange}>
        <DropdownMenu.Trigger asChild>{trigger}</DropdownMenu.Trigger>
        <DropdownMenu.Portal>
          <DropdownMenu.Content
            ref={ref}
            side={side}
            align={align}
            sideOffset={sideOffset}
            alignOffset={alignOffset}
            className={mergeClasses(
              menuContentVariants({ size }),
              'border-gray-200',
              className,
            )}
            {...props}
          >
            {children}
          </DropdownMenu.Content>
        </DropdownMenu.Portal>
      </DropdownMenu.Root>
    );
  },
);

Menu.displayName = 'Menu';

export const MenuComponent = Object.assign(Menu, {
  Item: MenuItem,
  Separator: MenuSeparator,
  Label: MenuLabel,
});

export { MenuItem, MenuSeparator, MenuLabel };
