<?php

declare(strict_types=1);

namespace App\Observers;

use App\Models\ProductAlternative;

final class ProductAlternativeObserver
{
    /**
     * Handle the ProductAlternative "created" event.
     * When creating A → B, automatically create B → A if it doesn't exist.
     */
    public function created(ProductAlternative $productAlternative): void
    {
        $reverseExists = ProductAlternative::where('product_id', $productAlternative->alternative_id)
            ->where('alternative_id', $productAlternative->product_id)
            ->exists();

        if (! $reverseExists) {
            ProductAlternative::withoutEvents(function () use ($productAlternative) {
                ProductAlternative::create([
                    'product_id' => $productAlternative->alternative_id,
                    'alternative_id' => $productAlternative->product_id,
                ]);
            });
        }
    }
}
