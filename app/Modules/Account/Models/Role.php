<?php

declare(strict_types=1);

namespace App\Modules\Account\Models;

use App\Modules\Account\Models\Factories\RoleFactory;
use App\Modules\Monite\Models\MoniteRoleMapping;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Permission\Models\Role as SpatieRole;

final class Role extends SpatieRole
{
    use HasFactory;
    use HasUuids;

    /**
     * Get the Monite role mappings for this role.
     */
    public function moniteRoleMappings(): HasMany
    {
        return $this->hasMany(MoniteRoleMapping::class);
    }

    protected static function newFactory(): RoleFactory
    {
        return RoleFactory::new();
    }
}
