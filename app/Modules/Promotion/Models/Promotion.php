<?php

declare(strict_types=1);

namespace App\Modules\Promotion\Models;

use App\Models\ProductOffer;
use App\Models\Vendor;
use App\Modules\Promotion\Enums\PromotionStatus;
use App\Modules\Promotion\Enums\PromotionType;
use App\Modules\Promotion\Exceptions\PromotionRuleAlreadyLastException;
use App\Modules\Promotion\Factories\PromotionFactory;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property-read PromotionType $type
 * @property-read PromotionStatus $status
 * @property-read \Illuminate\Support\Carbon $started_at
 * @property-read \Illuminate\Support\Carbon $ended_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, PromotionRule> $rules
 */
final class Promotion extends Model
{
    use HasFactory;
    use HasUuids;

    protected $guarded = [];

    public static function newFactory(): PromotionFactory
    {
        return PromotionFactory::new();
    }

    public function promotionable(): MorphTo
    {
        return $this->morphTo();
    }

    public function vendor(): BelongsTo
    {
        return $this->belongsTo(Vendor::class);
    }

    public function productOffers(): BelongsToMany
    {
        return $this->belongsToMany(ProductOffer::class, 'promotion_product_offers')->withTimestamps();
    }

    public function rules(): HasMany
    {
        return $this->hasMany(PromotionRule::class);
    }

    /**
     * Scope a query to only include active promotions
     */
    public function scopeActive(Builder $query): void
    {
        $query->where('status', PromotionStatus::Active)
            ->where('started_at', '<=', Carbon::now())
            ->where(function ($query) {
                $query->whereNull('ended_at')
                    ->orWhere('ended_at', '>=', Carbon::now());
            });
    }

    /**
     * @throws PromotionRuleAlreadyLastException
     */
    public function getRuleAfter(PromotionRule $rule): PromotionRule
    {
        if ($rule->priority === $this->rules->min('priority')) {
            throw new PromotionRuleAlreadyLastException($rule->id, $rule->priority);
        }

        return $this->rules
            ->sortByDesc('priority')
            ->where('priority', '<', $rule->priority)
            ->first();
    }

    protected function casts(): array
    {
        return [
            'type' => PromotionType::class,
            'status' => PromotionStatus::class,
            'started_at' => 'datetime',
            'ended_at' => 'datetime',
        ];
    }
}
