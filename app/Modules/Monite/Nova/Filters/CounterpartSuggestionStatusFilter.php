<?php

declare(strict_types=1);

namespace App\Modules\Monite\Nova\Filters;

use App\Modules\Monite\Enums\MoniteCounterpartSuggestionStatus;
use Laravel\Nova\Filters\Filter;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class CounterpartSuggestionStatusFilter extends Filter
{
    /**
     * The filter's component.
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     */
    public function apply(NovaRequest $request, $query, $value)
    {
        return $query->where('status', $value);
    }

    /**
     * Get the filter's available options.
     */
    public function options(NovaRequest $request): array
    {
        return MoniteCounterpartSuggestionStatus::options();
    }

    /**
     * Get the displayable name of the filter.
     */
    public function name(): string
    {
        return 'Status';
    }
}
