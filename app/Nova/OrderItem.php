<?php

declare(strict_types=1);

namespace App\Nova;

use App\Enums\OrderItemStatus;
use App\Nova\Actions\AttachOrderItemShipment;
use App\Nova\Actions\UpdateOrderItemStatus;
use App\Nova\Filters\OrderItemStatusFilter;
use App\Traits\Statusable;
use Illuminate\Http\Request;
use Laravel\Nova\Fields\Badge;
use Laravel\Nova\Fields\BelongsTo;
use Laravel\Nova\Fields\BelongsToMany;
use Laravel\Nova\Fields\Currency;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Select;
use Laravel\Nova\Fields\Text;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;

final class OrderItem extends Resource
{
    use Statusable;

    /**
     * The model the resource corresponds to.
     *
     * @var class-string<\App\Models\OrderItem>
     */
    public static $model = \App\Models\OrderItem::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'productOffer.name';

    /**
     * Disable the ability to create new resources.
     *
     * @var bool
     */
    public static $displayInNavigation = false;

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'productOffer.name',
        'productOffer.vendor.name',
    ];

    /**
     * The number of resources to show per page via relationships.
     *
     * @var int
     */
    public static $perPageViaRelationship = 20;

    /**
     * Determine if the current user can create new resources.
     */
    public static function authorizedToCreate(Request $request): bool
    {
        return false;
    }

    /**
     * Get the fields displayed by the resource.
     *
     * @return array
     */
    public function fields(NovaRequest $request)
    {
        return [
            BelongsTo::make('Order', 'order', Order::class)
                ->readonly()
                ->sortable(),

            BelongsTo::make('Product', 'productOffer', ProductOffer::class)
                ->readonly()
                ->searchable()
                ->sortable(),

            Currency::make('Price', 'price')
                ->currency('USD')
                ->asMinorUnits()
                ->sortable()
                ->required()
                ->min(0)
                ->step(0.01),

            Number::make('Quantity', 'quantity')->sortable(),

            Currency::make('Tax Fee', 'tax_fee')
                ->currency('USD')
                ->asMinorUnits()
                ->sortable()
                ->nullable()
                ->min(0)
                ->step(1),

            Currency::make('Total Price', 'total_price')
                ->onlyOnIndex()
                ->currency('USD')
                ->asMinorUnits()
                ->sortable(),

            Badge::make('Status', 'status')
                ->exceptOnForms()
                ->map($this->generateOptionMapping())
                ->labels(OrderItemStatus::options())
                ->sortable(),

            Select::make('Status', 'status')
                ->onlyOnForms()
                ->options(OrderItemStatus::options())
                ->displayUsingLabels()
                ->sortable(),

            Text::make('External ID', 'external_id')
                ->onlyOnDetail()
                ->sortable(),

            Text::make('External Status', 'external_status')
                ->onlyOnDetail()
                ->sortable(),

            Text::make('Error Message', 'error_message')
                ->onlyOnDetail()
                ->sortable(),

            BelongsToMany::make('Shipments', 'shipments', Shipment::class),
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @return array
     */
    public function cards(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @return array
     */
    public function filters(NovaRequest $request)
    {
        return [
            new OrderItemStatusFilter,
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @return array
     */
    public function lenses(NovaRequest $request)
    {
        return [];
    }

    /**
     * Get the actions available for the resource.
     *
     * @return array
     */
    public function actions(NovaRequest $request)
    {
        return [
            (new UpdateOrderItemStatus),
            (new AttachOrderItemShipment),
        ];
    }
}
