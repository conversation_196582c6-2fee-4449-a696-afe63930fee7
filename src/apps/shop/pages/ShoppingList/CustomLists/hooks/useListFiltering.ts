import { useMemo } from 'react';
import { ProductType } from '@/types';

type ListType = {
  createdBy: string;
  lastUpdated: string;
  name: string;
  products: ProductType[];
};

type UseListFilteringProps = {
  lists: ListType[];
  searchQuery: string;
};

type FilteredList = {
  isVisible: boolean;
} & ListType;

type UseListFilteringReturn = {
  filteredLists: FilteredList[];
};

export const useListFiltering = ({
  lists,
  searchQuery,
}: UseListFilteringProps): UseListFilteringReturn => {
  const filteredLists = useMemo(() => {
    if (!searchQuery) {
      return lists.map((list) => ({
        ...list,
        isVisible: true,
        sortedProducts: list.products,
      }));
    }

    return lists.map((list) => {
      const listNameMatches = list.name.toLowerCase().includes(searchQuery);

      if (listNameMatches) {
        return {
          ...list,
          isVisible: true,
          sortedProducts: list.products,
        };
      }

      const hasMatchingProducts = list.products.some((product) =>
        product.name.toLowerCase().includes(searchQuery),
      );

      return {
        ...list,
        isVisible: hasMatchingProducts,
      };
    });
  }, [lists, searchQuery]);

  return {
    filteredLists,
  };
};
